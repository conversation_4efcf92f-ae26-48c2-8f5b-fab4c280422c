# Migration TODO List

## Core Navigation

- [ ] MainTabView (Bottom Navigation)
  - [ ] People Tab
  - [ ] Community Tab
  - [ ] Messages Tab
  - [ ] Notifications Tab
  - [ ] Settings Tab
  - [ ] Dashboard Tab
  - [ ] Home Tab
- [ ] Navigation Graph Setup
- [ ] Deep Link Handler

## Onboarding Flow

- [ ] Welcome Screen
- [ ] Permissions Request Screen
  - [ ] Contacts Permission
  - [ ] Speech Recognition Permission
- [ ] Invite Flow Screens
  - [ ] Invite Landing Page
  - [ ] Invite Verification
  - [ ] Profile Setup
  - [ ] Profile Creation View

## People Section

- [ ] PeopleListView
  - [ ] Search Bar
  - [ ] Person Card Component
  - [ ] Draggable Person Component
- [ ] PersonProfileView
  - [ ] Profile Header
  - [ ] Contact Information Section
  - [ ] Notes Section
  - [ ] Action Buttons
- [ ] PersonSelectionView
- [ ] Your People View
  - [ ] Core Network Display
  - [ ] Connection Management

## Dashboard

- [ ] DashboardView

## Home

- [ ] HomeView

## Notifications

- [ ] NotificationsView

## Community Section

- [ ] CommunityView
  - [ ] Communities List
  - [ ] Post Feed
- [ ] CommunityDetailView
- [ ] PostView
  - [ ] Post Card Component
  - [ ] Interaction Stats
  - [ ] Comments Section
- [ ] CreatePostView
  - [ ] Post Type Selection
  - [ ] Community Selection
  - [ ] Media Upload
- [ ] DiscoverCommunitiesView

## Chat/Messages

- [ ] MessagesView (Chat List)
- [ ] ConversationDetailView
  - [ ] Message Bubbles
  - [ ] Input Field
  - [ ] Media Attachments
- [ ] CreateChatView
  - [ ] Participant Selection
  - [ ] Group Chat Setup
- [ ] ParticipantsListView

## Settings

- [ ] SettingsView
  - [ ] Profile Settings
  - [ ] Dark Mode Toggle
  - [ ] Notification Settings
- [ ] UserSelectionView (Demo Mode)
- [ ] UXProfileSettingsView

## Data Model

- [ ] UserProfile

## Shared Components

- [ ] AvatarView
- [ ] TagView
- [ ] InteractionStat
- [ ] TooltipView
- [ ] CachedAsyncImage
- [ ] LoadingIndicators

## Peeps AI Integration

- [ ] PeepsButton
  - [ ] Voice Input UI
  - [ ] Text Input UI
- [ ] AI Response Display
- [ ] RecommendationsView

## Modals and Sheets

- [ ] Community Creation Sheet
- [ ] Post Creation Sheet
- [ ] Profile Editor Sheet
- [ ] Image Viewer
- [ ] Permission Request Dialogs

## Additional Features

- [ ] Offline Support
- [ ] Image Upload/Download
- [ ] Push Notifications
- [ ] Deep Linking
- [ ] In-App Purchases

## Testing

- [ ] Unit Tests for ViewModels
- [ ] UI Tests for Core Flows
- [ ] Integration Tests
- [ ] Performance Testing

## Performance Optimizations

- [ ] Baseline Profile Generation
- [ ] Compose Compiler Metrics Setup
- [ ] Memory Usage Optimization
- [ ] Image Loading Strategy

## Documentation

- [ ] Architecture Documentation
- [ ] Component Usage Guidelines
- [ ] Theme Documentation
- [ ] API Documentation

## Notes

- Each screen should follow Material 3 design guidelines
- Implement adaptive layouts for different screen sizes
- Consider tablet/foldable layouts
- Maintain consistent animation patterns
- Follow accessibility best practices
- **Legacy Note:** The legacy-archive folder contains XML-based layouts, navigation, and resources (e.g., activity*main.xml, fragment*\*.xml, mobile_navigation.xml, styles.xml, themes.xml, etc.). All new Compose code should refactor away from these XML configs and use Compose-native navigation, theming, and UI components.

## Migration Priority

1. Core Navigation & Main Screens
2. People Section (Core Feature)
3. Community Features
4. Chat/Messages
5. Settings & Profile
6. AI Integration
7. Additional Features & Polish

## Passkey Features

### Recovery

- [x] Implemented passkey recovery initiation (email input, request to backend)
- [x] Implemented passkey recovery registration challenge request from backend
- [x] Implemented client-side passkey creation/registration using Android's Credential Manager
- [x] Implemented passkey recovery verification step with the backend
- [x] Updated Recovery UI to show success/error messages (Snackbar) and navigate back

### Login (Future)

- [x] Create a login page/flow to allow users to sign in with their existing passkeys.
