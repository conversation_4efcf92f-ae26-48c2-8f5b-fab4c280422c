<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2021 Google LLC
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->
<code_scheme name="nowinandroid" version="1">
  <option name="RIGHT_MARGIN" value="100" />
  <AndroidXmlCodeStyleSettings>
    <option name="USE_CUSTOM_SETTINGS" value="true" />
  </AndroidXmlCodeStyleSettings>
  <JavaCodeStyleSettings>
    <option name="FIELD_NAME_PREFIX" value="m" />
    <option name="STATIC_FIELD_NAME_PREFIX" value="s" />
    <option name="ANNOTATION_PARAMETER_WRAP" value="1" />
    <option name="INSERT_INNER_CLASS_IMPORTS" value="true" />
    <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="9999" />
    <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="9999" />
    <option name="IMPORT_LAYOUT_TABLE">
      <value>
        <package name="android" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="com.android" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="dalvik" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="libcore" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="com" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="gov" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="junit" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="net" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="org" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="java" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="javax" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="" withSubpackages="true" static="true" />
        <emptyLine />
        <package name="android" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="com.android" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="dalvik" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="libcore" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="com" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="gov" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="junit" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="net" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="org" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="java" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="javax" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="false" />
      </value>
    </option>
    <option name="JD_P_AT_EMPTY_LINES" value="false" />
    <option name="JD_DO_NOT_WRAP_ONE_LINE_COMMENTS" value="true" />
    <option name="JD_KEEP_EMPTY_PARAMETER" value="false" />
    <option name="JD_KEEP_EMPTY_EXCEPTION" value="false" />
    <option name="JD_KEEP_EMPTY_RETURN" value="false" />
    <option name="JD_PRESERVE_LINE_FEEDS" value="true" />
  </JavaCodeStyleSettings>
  <JetCodeStyleSettings>
    <option name="PACKAGES_TO_USE_STAR_IMPORTS">
      <value />
    </option>
    <option name="NAME_COUNT_TO_USE_STAR_IMPORT" value="99" />
    <option name="NAME_COUNT_TO_USE_STAR_IMPORT_FOR_MEMBERS" value="99" />
    <option name="IMPORT_NESTED_CLASSES" value="true" />
    <option name="CONTINUATION_INDENT_IN_PARAMETER_LISTS" value="false" />
    <option name="CONTINUATION_INDENT_IN_ARGUMENT_LISTS" value="false" />
    <option name="CONTINUATION_INDENT_FOR_EXPRESSION_BODIES" value="false" />
    <option name="CONTINUATION_INDENT_FOR_CHAINED_CALLS" value="false" />
    <option name="CONTINUATION_INDENT_IN_SUPERTYPE_LISTS" value="false" />
    <option name="CONTINUATION_INDENT_IN_IF_CONDITIONS" value="false" />
    <option name="WRAP_EXPRESSION_BODY_FUNCTIONS" value="1" />
    <option name="IF_RPAREN_ON_NEW_LINE" value="true" />
  </JetCodeStyleSettings>
  <Properties>
    <option name="KEEP_BLANK_LINES" value="true" />
  </Properties>
  <XML>
    <option name="XML_ATTRIBUTE_WRAP" value="2" />
    <option name="XML_ALIGN_ATTRIBUTES" value="false" />
    <option name="XML_SPACE_INSIDE_EMPTY_TAG" value="true" />
    <option name="XML_LEGACY_SETTINGS_IMPORTED" value="true" />
  </XML>
  <ADDITIONAL_INDENT_OPTIONS fileType="java">
    <option name="TAB_SIZE" value="8" />
  </ADDITIONAL_INDENT_OPTIONS>
  <ADDITIONAL_INDENT_OPTIONS fileType="js">
    <option name="CONTINUATION_INDENT_SIZE" value="4" />
  </ADDITIONAL_INDENT_OPTIONS>
  <codeStyleSettings language="JAVA">
    <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
    <option name="ALIGN_MULTILINE_FOR" value="false" />
    <option name="CALL_PARAMETERS_WRAP" value="1" />
    <option name="PREFER_PARAMETERS_WRAP" value="true" />
    <option name="METHOD_PARAMETERS_WRAP" value="1" />
    <option name="RESOURCE_LIST_WRAP" value="1" />
    <option name="EXTENDS_LIST_WRAP" value="1" />
    <option name="THROWS_LIST_WRAP" value="1" />
    <option name="EXTENDS_KEYWORD_WRAP" value="1" />
    <option name="THROWS_KEYWORD_WRAP" value="1" />
    <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
    <option name="BINARY_OPERATION_WRAP" value="1" />
    <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="true" />
    <option name="TERNARY_OPERATION_WRAP" value="1" />
    <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
    <option name="FOR_STATEMENT_WRAP" value="1" />
    <option name="ARRAY_INITIALIZER_WRAP" value="1" />
    <option name="ASSIGNMENT_WRAP" value="1" />
    <option name="IF_BRACE_FORCE" value="3" />
    <option name="DOWHILE_BRACE_FORCE" value="3" />
    <option name="WHILE_BRACE_FORCE" value="3" />
    <option name="FOR_BRACE_FORCE" value="3" />
    <option name="WRAP_LONG_LINES" value="true" />
    <option name="PARAMETER_ANNOTATION_WRAP" value="1" />
    <option name="VARIABLE_ANNOTATION_WRAP" value="1" />
    <option name="ENUM_CONSTANTS_WRAP" value="1" />
  </codeStyleSettings>
  <codeStyleSettings language="JSON">
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
      <option name="TAB_SIZE" value="2" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="XML">
    <option name="FORCE_REARRANGE_MODE" value="1" />
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
    <arrangement>
      <rules>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>xmlns:android</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>^$</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>xmlns:.*</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>^$</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:id</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:name</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>name</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>^$</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>style</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>^$</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:layout_width</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:layout_height</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:layout_.*</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:width</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:height</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:viewportWidth</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:viewportHeight</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res/android</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:layout_.*</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res-auto</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>http://schemas.android.com/apk/res-auto</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*:layout_.*</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>.*</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
        <section>
          <rule>
            <match>
              <AND>
                <NAME>.*</NAME>
                <XML_ATTRIBUTE />
                <XML_NAMESPACE>.*</XML_NAMESPACE>
              </AND>
            </match>
            <order>BY_NAME</order>
          </rule>
        </section>
      </rules>
    </arrangement>
  </codeStyleSettings>
  <codeStyleSettings language="kotlin">
    <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
    <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="0" />
    <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
    <option name="CALL_PARAMETERS_WRAP" value="1" />
    <option name="CALL_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
    <option name="CALL_PARAMETERS_RPAREN_ON_NEXT_LINE" value="true" />
    <option name="METHOD_PARAMETERS_WRAP" value="5" />
    <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
    <option name="METHOD_PARAMETERS_RPAREN_ON_NEXT_LINE" value="true" />
    <option name="EXTENDS_LIST_WRAP" value="1" />
    <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
    <option name="ASSIGNMENT_WRAP" value="1" />
    <option name="FIELD_ANNOTATION_WRAP" value="1" />
    <option name="PARAMETER_ANNOTATION_WRAP" value="1" />
    <option name="VARIABLE_ANNOTATION_WRAP" value="1" />
    <option name="ENUM_CONSTANTS_WRAP" value="5" />
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
</code_scheme>