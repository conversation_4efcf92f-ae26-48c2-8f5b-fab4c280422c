// This file contains classes (with possible wildcards) that the Compose Compiler will treat as stable.
// It allows us to define classes that are not part of our codebase without wrapping them in a stable class.
// For more information, check https://developer.android.com/jetpack/compose/performance/stability/fix#configuration-file

// We always use immutable classes for our data model, to avoid running the Compose compiler
// in the module we declare it to be stable here.
ai.peepsapp.peopleapp.core.model.data.*

// Java standard library classes
java.time.ZoneId
java.time.ZoneOffset
