#!/usr/bin/env bash
# scripts/check_includes.sh

# Find the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# Assume project root is the parent of scripts/
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

cd "$PROJECT_ROOT" || exit 1

missing=0
grep '^include(' settings.gradle.kts | \
  sed -E 's/include\((.*)\)/\1/' | \
  tr -d '"' | \
  grep '^:' | \
  while read m; do
    d=$(echo $m | sed 's/^://;s/:/\//g')
    if [ ! -d "$d" ]; then
      echo "Missing directory for $d"
      missing=1
    elif [ ! -f "$d/build.gradle.kts" ] && [ ! -f "$d/build.gradle" ]; then
      echo "Missing build file in $d"
      missing=1
    fi
  done

if [ "$missing" -eq 0 ]; then
  echo "All included modules have corresponding directories and build files."
fi
