# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Ensure important default jvmargs aren't overwritten. See https://github.com/gradle/gradle/issues/19750
#
# For more information about how Gradle memory options were chosen:
# - Metaspace See https://www.jasonpearson.dev/metaspace-in-jvm-builds/
# - SoftRefLRUPolicyMSPerMB would default to 1000 which with a 4gb heap translates to ~51 minutes.
#   A value of 1 means ~4 seconds before SoftRefs can be collected, which means its realistic to
#   collect them as needed during a build that should take seconds to minutes.
# - CodeCache normally defaults to a very small size. Increasing it from platform defaults of 32-48m
#   because of how many classes can be loaded into memory and then cached as native compiled code
#   for a small speed boost.
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g

# For more information about how Kotlin Daemon memory options were chosen:
# - Kotlin JVM args only inherit Xmx, ReservedCodeCache, and MaxMetaspace. Since we are specifying
#   other args we need to specify all of them here.
# - We're using the Kotlin Gradle Plugin's default value for ReservedCodeCacheSize, if we do not then
#   the Gradle JVM arg value for ReservedCodeCacheSize will be used.
# kotlin.daemon.jvmargs=-Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:SoftRefLRUPolicyMSPerMB=1 -XX:ReservedCodeCacheSize=320m -XX:+HeapDumpOnOutOfMemoryError -Xmx4g -Xms4g # Commenting out for simplicity

# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true

# Not encouraged by Gradle and can produce weird results. Wait for isolated projects instead.
org.gradle.configureondemand=false

# Enable caching between builds.
org.gradle.caching=true

# Enable configuration caching between builds.
org.gradle.configuration-cache=true
org.gradle.configuration-cache.parallel=true
# This option is set because of https://github.com/google/play-services-plugins/issues/246
# to generate the Configuration Cache regardless of incompatible tasks.
# See https://github.com/android/nowinandroid/issues/1022 before using it.
org.gradle.configuration-cache.problems=warn

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app"s APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official

# Disable build features that are enabled by default,
# https://developer.android.com/build/releases/gradle-plugin#default-changes
android.defaults.buildfeatures.resvalues=false
android.defaults.buildfeatures.shaders=false

# Run Roborazzi screenshot tests with the local tests
roborazzi.test.verify=true

# Prevent uninstall app after instrumented tests
# https://issuetracker.google.com/issues/295039976
android.injected.androidTest.leaveApksInstalledAfterRun=true
