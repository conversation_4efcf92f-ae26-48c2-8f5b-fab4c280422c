# People App v0.1 - Prototype
## Product Requirements Document

### Overview
The v0.1 release serves as a functional prototype, providing a horizontal slice across all core aspects of the People App. This version demonstrates the fundamental value proposition while acknowledging specific limitations that will be addressed in subsequent releases.

### Objectives
- Create a functional prototype that showcases the core value of the People App
- Implement essential functionality across all main features
- Validate core user flows and gather feedback for future iterations

### Target Audience
- Early adopters invited to test the application
- Internal stakeholders for product validation

### Feature Requirements

#### 1. Onboarding
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| ON-1 | Display demo mode disclaimer at first launch | HIGH | Clear explanation of prototype limitations |
| ON-2 | Request speech access permission | HIGH | Required for voice interaction with <PERSON>eeps agent |
| ON-3 | Implement invite-only access system | HIGH | Users can only join via invitation |
| ON-4 | Display inviter information on profile | MEDIUM | Always show who invited a user |
| ON-5 | Create shared profile with required fields | HIGH | Photo, role, company, bio, contact info, social links |

#### 2. People Tab
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| PE-1 | Enable profile viewing | HIGH | Users can view complete profiles of connections |
| PE-2 | Support adding private notes to profiles | MEDIUM | Notes visible only to the creator |
| PE-3 | Create "Your People" core network | HIGH | Primary connections for each user |
| PE-4 | Implement community creation option | MEDIUM | Placeholder for community functionality |
| PE-5 | Enable community discovery | MEDIUM | Basic browsing of available communities |
| PE-6 | Implement contacts access option | MEDIUM | With proper permission handling |
| PE-7 | Create invite system | HIGH | Allow users to invite others to the platform |

#### 3. Community Tab
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| CO-1 | Implement "Update" post type | HIGH | For sharing updates with community |
| CO-2 | Implement "Ask" post type | HIGH | For asking questions to community |
| CO-3 | Provide AI recommendations | MEDIUM | Personalized to individual users |

#### 4. Peeps Button
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| PB-1 | Create directional button UI | HIGH | Access agent via voice or text |
| PB-2 | Implement agent functionality | HIGH | Accesses people, communities and chats for answers |

#### 5. Chat
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| CH-1 | Enable direct messaging | HIGH | One-on-one conversations |
| CH-2 | Implement read status | MEDIUM | Show when messages have been read |
| CH-3 | Support group messaging | MEDIUM | Multiple participants in a conversation |

#### 6. Settings
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| SE-1 | Enable profile editing | HIGH | Users can update their own profiles |
| SE-2 | Implement dark mode toggle | LOW | UI theme switching |
| SE-3 | Add notifications toggle | MEDIUM | Enable/disable app notifications |

### Technical Requirements

#### Authentication & Security
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| TS-1 | Implement secure invitation system | HIGH | Preventing unauthorized access |
| TS-2 | Secure storage of user data | HIGH | Encryption for sensitive information |
| TS-3 | Permission handling for device features | HIGH | Speech, contacts, etc. |

#### User Interface
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| UI-1 | Implement Jetpack Compose UI | HIGH | All screens using Compose |
| UI-2 | Support dark/light themes | MEDIUM | Consistent across all screens |
| UI-3 | Create responsive layouts | MEDIUM | Support various device sizes |

#### Performance
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| PF-1 | Ensure smooth scrolling in lists | MEDIUM | No jank in profile/post lists |
| PF-2 | Optimize image loading | MEDIUM | Fast profile picture loading |
| PF-3 | Minimize app startup time | MEDIUM | Under 2 seconds on reference device |

### Known Limitations (To be addressed in future versions)
- In-app purchase for community creation (v0.5)
- Basic reputation tracking for invites (v0.2)
- Read status on posts and showing new items in timeline (v0.2)
- Images in posts (v0.2)
- Image messages in chat (v0.2)
- Group chats for introductions (v0.2)

### Acceptance Criteria
- Users can successfully complete onboarding via invitation
- Profiles can be created and viewed
- "Your People" network can be established
- Basic posts (updates and asks) can be created and viewed
- Peeps agent responds to basic voice and text queries
- Direct messaging works with read status
- All settings options function correctly

### Out of Scope (For Future Versions)
- Profile creation from contacts
- Vector database and RAG implementation
- Auto link creation in posts
- Drag-and-drop functionality for contacts
- LinkedIn API integration
- Relationship expressions
- Extended occupation details
- Public profile functionality
- Third-party integrations (Slack, WhatsApp)
- Private journaling
- Vitals data integration
- Live photos support
- AI summary widget for Home Screen