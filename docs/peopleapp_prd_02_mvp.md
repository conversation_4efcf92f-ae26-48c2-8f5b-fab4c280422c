# People App v0.2 - MVP
## Product Requirements Document

### Overview
The v0.2 release represents the Minimum Viable Product (MVP) of the People App, delivering a horizontal slice of the core experience with refined functionality. This version focuses on creating a seamless onboarding experience, establishing the user's core network, and enabling meaningful social interactions.

### Objectives
- Deliver a polished, user-friendly MVP that demonstrates the core value proposition
- Implement passwordless authentication for frictionless onboarding
- Enable core networking and communication features
- Integrate AI functionality to enhance relationship navigation

### Target Audience
- Initial set of invited users
- Early adopters looking for a modern professional networking solution

### Feature Requirements

#### 1. Authentication & Onboarding
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| AU-1 | Implement passkey-based authentication (FIDO2/WebAuthn) | HIGH | Passwordless, secure access |
| AU-2 | Create invite-only access system | HIGH | Users can only join via secure invitation links |
| AU-3 | Generate persistent session tokens | HIGH | Securely stored on-device |
| AU-4 | Develop profile-first setup flow | HIGH | Profile becomes the user's identity |
| AU-5 | Support biometric passkey linking | HIGH | Via Face ID, fingerprint, Windows Hello |
| AU-6 | Enable per-device passkey registration | MEDIUM | Each device gets its own passkey |

#### 2. Profiles
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| PR-1 | Support manual profile creation | HIGH | User inputs all profile information |
| PR-2 | Enable contact import for profile creation | HIGH | Create profile from device contacts |
| PR-3 | Implement core profile fields | HIGH | Photo, name, role, company, bio, contact info |
| PR-4 | Support private notes on profiles | MEDIUM | Visible only to the creator |
| PR-5 | Enable profile editing and updating | HIGH | Users can modify their profiles |

#### 3. Core Network (Your People)
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| CN-1 | Implement "Your People" network | HIGH | Core connections for each user |
| CN-2 | Limit initial invites to 3 people | HIGH | Controlled growth strategy |
| CN-3 | Create profile search functionality | HIGH | Find and add people to core network |
| CN-4 | Implement contact permission handling | HIGH | Proper access to device contacts |
| CN-5 | Create profile viewing experience | HIGH | Clean, informative profile display |

#### 4. Posts
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| PO-1 | Implement "Update" post type | HIGH | For sharing updates with network |
| PO-2 | Implement "Ask" post type | HIGH | For asking questions to network |
| PO-3 | Support image uploads in posts | HIGH | Single or multiple images per post |
| PO-4 | Enable likes on posts | MEDIUM | Basic engagement metric |
| PO-5 | Enable comments on posts | MEDIUM | Thread-based discussions |
| PO-6 | Implement global read/unread status | MEDIUM | Track which posts a user has seen |

#### 5. Introductions
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| IN-1 | Enable one-message introductions | HIGH | Simple connection facilitation |
| IN-2 | Implement global read/unread status | MEDIUM | Track viewed introductions |
| IN-3 | Create introduction UI flow | HIGH | Intuitive process for connecting people |

#### 6. Peeps AI
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| AI-1 | Implement text interaction | HIGH | Text-based AI queries |
| AI-2 | Implement voice-to-text on device | HIGH | Voice-based AI interaction |
| AI-3 | Create deep links to content | MEDIUM | AI can point to specific content |
| AI-4 | Enable AI-assisted post drafting | MEDIUM | AI helps create posts |
| AI-5 | Integrate with profile and post data | HIGH | AI understands user's network |

### Technical Requirements

#### Authentication & Security
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| TS-1 | Implement FIDO2/WebAuthn standards | HIGH | Industry-standard passwordless auth |
| TS-2 | Secure invitation system | HIGH | Preventing unauthorized access |
| TS-3 | Secure on-device storage | HIGH | For tokens and sensitive data |
| TS-4 | Platform-specific passkey integration | HIGH | iOS Keychain, Google Password Manager |

#### User Interface
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| UI-1 | Implement consistent design system | HIGH | Unified look and feel |
| UI-2 | Create responsive layouts | HIGH | Support various device sizes |
| UI-3 | Optimize media viewing experience | MEDIUM | Fast, high-quality image display |
| UI-4 | Implement smooth animations | LOW | Polish the user experience |

#### Performance
| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| PF-1 | Optimize app startup time | HIGH | Under 1.5 seconds on reference device |
| PF-2 | Ensure smooth scrolling in feeds | HIGH | No jank in post lists |
| PF-3 | Efficient image loading and caching | HIGH | Fast profile and post image loading |
| PF-4 | Minimize battery impact | MEDIUM | Efficient background processes |

### Cross-Platform Considerations

#### Passkey Platform Support
| Scenario | Status | Requirements |
|----|-------------|----------|
| iOS → iOS | Supported | Uses iCloud Keychain for seamless sync |
| Android → Android | Supported | Uses Google Password Manager for seamless sync |
| iOS ↔ Android | Limited | Future enhancement - out of scope for MVP |
| Mobile → Desktop | Supported | Works with same ecosystem account |
| Different Devices, Same Ecosystem | Supported | Uses cloud keychain services |
| Different Devices, Different Ecosystems | Limited | Future enhancement - out of scope for MVP |

### Acceptance Criteria
- Users can successfully onboard via invitation using passwordless authentication
- Profiles can be created manually or imported from contacts
- Users can build their core network with up to 3 people
- Posts (updates and asks) can be created with text and images
- Comments and likes on posts work correctly
- Introductions can be made between connections
- Peeps AI responds accurately to text and voice queries
- AI can draft posts based on user input
- All features work consistently across supported devices

### Out of Scope (For Future Versions)
- Chat functionality (1-on-1, group)
- Communities and events
- Reminders
- Advanced AI beyond post drafting
- Post feedback and reputation systems
- Journaling
- Social media integrations beyond contact reading
- Vector database implementation
- Cross-platform passkey synchronization
- Account recovery processes