# WSL Development Setup Guide

This guide explains how to set up Android development using WSL (Windows Subsystem for Linux) with Android Studio's emulator.

## Environment Overview

The setup uses a hybrid approach:
- Project files and builds run in WSL
- Android Studio is used only for the emulator
- Java/Kotlin compilation happens in WSL
- Android SDK and emulator run on Windows

### Why This Approach?
This setup provides:
1. Better development environment in WSL
2. Fast emulator performance on Windows
3. Simple configuration with no path switching needed

## Prerequisites

1. Windows with WSL2 installed (Ubuntu recommended)
2. Android Studio installed on Windows
3. Android SDK installed through Android Studio
4. OpenJDK 17 installed in WSL

## One-Time Setup

### 1. Java Setup in WSL

```bash
# Verify Java installation
java -version  # Should show OpenJDK 17
echo $JAVA_HOME  # Should show /usr/lib/jvm/java-17-openjdk-amd64
```

### 2. Run Setup Script

The setup script will:
- Create ADB wrapper to use Windows ADB
- Configure permanent WSL paths
- Set up environment variables

```bash
# Make script executable
chmod +x setup-android-tools.sh

# Run setup
./setup-android-tools.sh
source ~/.bashrc
```

## Development Workflow

### Building from WSL

Build the app using Gradle:
```bash
./gradlew assembleDemoDebug
```

### Using the Emulator

1. Launch Android Studio normally from Windows
2. Start the emulator from Android Studio
3. Install APK using ADB from WSL:
   ```bash
   adb install -r app/build/outputs/apk/demo/debug/app-demo-debug.apk
   ```

## Common Issues and Solutions

1. **ADB Connection Issues**:
   - Verify emulator is running in Android Studio
   - Check `adb devices` output
   - Restart ADB server if needed: `adb kill-server && adb start-server`

2. **Build Failures**:
   - Verify Java version: `java -version`
   - Check JAVA_HOME is set correctly
   - Ensure Android SDK path in local.properties is correct

## Best Practices

1. **Keep Android Studio Simple**:
   - Use it primarily for the emulator
   - Don't worry about Gradle sync warnings
   - Build and develop from WSL command line

2. **Efficient Development**:
   - Keep emulator running in Android Studio
   - Use WSL terminal for all commands
   - Use your preferred editor in WSL

## Notes

- All builds should be done from WSL
- Android Studio is used only for the emulator
- Java 17 is required for this project
- Keep Android SDK in Windows for best emulator performance 