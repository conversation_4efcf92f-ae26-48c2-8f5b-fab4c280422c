# ADR 001: Use AndroidX CredentialManager for Passkey Operations

## Status

Accepted

## Context

Implementing passkey registration and authentication requires interacting with the Android FIDO APIs. Previously, this might have involved using `Fido2ApiClient` directly, managing `PendingIntent`s, and handling activity results manually. Google now recommends using the **AndroidX Credential Manager API** (`androidx.credentials.*`) for these tasks.

## Decision

We will use the `androidx.credentials.CredentialManager` for all passkey creation (`createCredential`) and authentication (`getCredential`) flows within the Android application. Direct usage of `Fido2ApiClient` for launching intents (`getRegisterPendingIntent`, `getSignPendingIntent`) should be avoided for new implementations and refactored where encountered.

## Rationale

-   **Unified API:** `CredentialManager` provides a single, modern API surface for various credential types, including passkeys, passwords, and federated sign-in (like Sign in with Google), simplifying integration.
-   **Google Recommendation:** This aligns with Google's current recommendation and migration guides (see [Migrate from FIDO2 to Credential Manager](https://developer.android.com/identity/sign-in/fido2-migration)).
-   **Simplified Flow:** It encapsulates the underlying `PendingIntent` launching and result handling, often leading to cleaner ViewModel/Repository logic compared to manual `ActivityResultLauncher` management for FIDO operations.
-   **Future Compatibility:** Using the recommended Jetpack library is more likely to receive updates and remain compatible with future Android versions and Play Services changes.

## Consequences

-   Developers need to be familiar with the `CredentialManager` API (`createCredential`, `getCredential`, request objects like `CreatePublicKeyCredentialRequest`, and response handling).
-   Existing code using `Fido2ApiClient` directly for intents might need refactoring.
-   Requires `androidx.credentials:credentials` and `androidx.credentials:credentials-play-services-auth` dependencies.
-   ViewModel/Repository logic will interact with `CredentialManager` suspend functions, likely requiring an `Activity` context for calls that invoke UI.

## Implementation Example

See `AuthViewModel.kt` for an example of using `CredentialManager` to initiate passkey creation. 