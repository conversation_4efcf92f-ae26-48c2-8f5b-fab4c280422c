# ADR 003: Guidelines for Legacy Feature Removal

## Status

Accepted

## Context

The initial codebase contained several features (News, Topics, Search, Sync, Notifications) inherited from the "Now in Android" sample application. These features were not part of the PeepsApp MVP and were causing build failures, dependency conflicts, and general complexity. A significant effort was undertaken to remove these legacy features to streamline the codebase and focus on core PeepsApp functionality. This process highlighted the need for a systematic approach to such removals.

## Decision

When removing significant legacy features or modules, the following multi-step process should be adopted to ensure a clean and thorough removal, minimizing build breaks and residual orphaned code:

1.  **Identify Core Feature Modules/Packages:**
    *   Locate the primary Gradle modules (e.g., `feature/feature_name`, `sync/work`) or top-level packages within core modules (e.g., `core/data/news`, `core/model/topic`) that house the feature's main logic, UI, and data definitions.

2.  **Bottom-Up Deletion and DI Cleanup (Iterative):**
    *   **Models/Entities:** Start by deleting data classes, model definitions, and database entities related to the feature (typically in `:core:model`, `:core:database/model`).
    *   **DAOs:** Delete Data Access Objects related to the feature (typically in `:core:database/dao`). Update the main Room `Database` class to remove references to these DAOs and entities.
    *   **Network Models & API Services:** Delete network request/response models and Retrofit API service interfaces (typically in `:core:network/model`, `:core:network/api`).
    *   **Repositories (Interfaces & Implementations):** Delete repository interfaces and their implementations (e.g., `OfflineFirst...`, `Default...`, `Composite...`) that consume the deleted DAOs and network services (typically in `:core:data/repository`).
    *   **Use Cases/Interactors:** Delete domain layer use cases or interactors that depend on the removed repositories (typically in `:core:domain`).
    *   **Viewmodels & UI Components:** Delete ViewModels, Composable screens, and UI helper classes/widgets directly related to the feature (typically in feature-specific modules or `:core:ui`).
    *   **Dependency Injection (DI) Modules:**
        *   Remove Hilt `@Binds` or `@Provides` methods for the deleted classes from relevant DI modules (e.g., `DataModule`, `NetworkModule`, `DaosModule`).
        *   If a DI module becomes entirely empty or solely provided dependencies for the removed feature, delete the module file itself. Pay attention to modules in different source sets (e.g., `main`, `prod`, `demo`).
    *   **Test Code:** Delete corresponding test files, fake implementations, and test data generators from test source sets (e.g., `:core:testing`, `:core:data-test`, unit test folders within modules). Update test DI modules (e.g., `TestDataModule`) to remove bindings for deleted fakes.
    *   **Helper/Util Classes:** Delete any utility or helper classes that were exclusively used by the removed feature.

3.  **Build & Iterate:**
    *   After each significant deletion step (or a few related ones), run a full Gradle build (e.g., `./gradlew assembleDebug`).
    *   Address any compilation errors or KSP/Hilt errors. These will often point to remaining usages or DI issues.
    *   This iterative process of delete-build-fix is crucial for uncovering all dependencies.

4.  **Module-Level Cleanup:**
    *   **Remove Gradle Module Dependencies:** Once a feature module is gutted or if a core module no longer needs to depend on a removed feature module, remove the `implementation(projects...)` or `api(projects...)` dependency from the `build.gradle.kts` files of dependent modules.
    *   **Remove from `settings.gradle.kts`:** If an entire Gradle module (e.g., `:feature:news`, `:sync:work`) has been effectively removed or is no longer needed, remove its `include(...)` line from the root `settings.gradle.kts`.

5.  **Final Check & Verification:**
    *   After the build is successful, run the application on an emulator/device to ensure no runtime crashes occur due to missing classes or resources.
    *   Perform a codebase search for any remaining import statements or qualified names related to the removed feature's packages or key classes to catch stray references.

## Rationale

-   **Systematic Approach:** Following a structured, bottom-up approach helps manage the complexity of removing interconnected code.
-   **Reduced Build Failures:** Iterative building and fixing catch errors early and prevent them from cascading.
-   **Thoroughness:** Ensures that not just the main logic but also associated data models, DI bindings, tests, and module dependencies are cleanly removed.
-   **Maintainability:** A cleaner codebase with no dead code is easier to understand, maintain, and build upon.
-   **Build Speed:** Removing unused modules and dependencies can contribute to faster build times.

## Consequences

-   Requires diligent attention to detail and an understanding of how different parts of the application are connected.
-   May be time-consuming for deeply integrated features.
-   Risk of accidentally deleting shared code if not careful; version control is crucial. 