# ADR 002: Use Retrofit with Kotlinx Serialization for Networking

## Status

Accepted

## Context

The application requires communication with a backend REST API (`stage.peepsapp.ai`) to handle authentication (challenge/verify for passkeys), user data, etc. A robust and maintainable networking layer is needed. Options include using libraries like Retrofit, Ktor, Volley, or implementing calls directly with OkHttp. A mechanism for parsing JSON request/response bodies is also required, with common choices being Gson or Kotlinx Serialization.

## Decision

We will use **Retrofit** as the HTTP client library for making network requests. **Kotlinx Serialization** (`kotlinx.serialization`) will be used for serializing Kotlin data classes to JSON and deserializing JSON responses back into data classes. The specific Retrofit converter library used will be `com.jakewharton.retrofit:retrofit2-kotlinx-serialization-converter`. OkHttp will be used under the hood by Retrofit, configured with necessary interceptors (like logging). Hilt will be used to provide instances of `Json`, `OkHttpClient`, `Retrofit`, and the specific `AuthApiService` interface.

## Rationale

-   **Industry Standard:** Retrofit is a widely adopted, mature, and well-documented networking library for Android.
-   **Type Safety:** Retrofit's interface-based definition provides compile-time safety for API endpoints and parameters.
-   **Kotlin Native:** Kotlinx Serialization is the standard, idiomatic serialization library for Kotlin, offering better integration with Kotlin features (like default values, sealed classes) compared to Gson.
-   **Converter Availability:** The Jake Wharton converter seamlessly integrates Kotlinx Serialization with Retrofit. (We initially had a conflict using the Square converter, confirming the choice of the Jake Wharton one).
-   **Coroutine Support:** Retrofit has excellent built-in support for Kotlin Coroutines (`suspend` functions), fitting our asynchronous programming model.
-   **Testability:** Defining API endpoints as interfaces makes mocking and testing easier.
-   **Extensibility:** Retrofit and OkHttp allow for easy addition of interceptors for logging, authentication headers, etc.

## Consequences

-   Dependencies: Requires adding `com.squareup.retrofit2:retrofit`, `com.squareup.okhttp3:okhttp` (and logging interceptor), `org.jetbrains.kotlinx:kotlinx-serialization-json`, and `com.jakewharton.retrofit:retrofit2-kotlinx-serialization-converter`.
-   Boilerplate: Requires defining Kotlin data classes (`@Serializable`) for all API request/response bodies and a Retrofit interface (`@GET`, `@POST`, etc.) for API endpoints.
-   Hilt Setup: Requires a Hilt module (`NetworkModule`) to configure and provide networking components.
-   Build Plugin: Requires the `kotlinx-serialization` Gradle plugin.

## Implementation Example

See `core/network/src/main/kotlin/ai/peepsapp/peopleapp/core/network/di/NetworkModule.kt`, `core/network/src/main/kotlin/ai/peepsapp/peopleapp/core/network/retrofit/AuthApiService.kt`, and `core/network/src/main/kotlin/ai/peepsapp/peopleapp/core/network/model/AuthApiModel.kt` for implementation details. 