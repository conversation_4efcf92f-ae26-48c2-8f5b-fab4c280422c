# Jetpack Compose State Management Best Practices (PeepsApp)

This guide outlines recommended practices for managing state in Jetpack Compose within the PeepsApp project. Adhering to these guidelines will help maintain consistency, readability, and performance.

## Core Concepts

State in Compose determines what is shown on the UI. When state changes, Composables that read that state are recomposed to reflect the new UI.

- **`State<T>`:** An observable holder of a value. Composables reading a `State` object will be recomposed when its value changes.
- **`MutableState<T>`:** A `State` whose value can be changed.
- **`remember`:** Used to store objects (including `MutableState`) in memory during composition and across recompositions.
- **`ViewModel`:** Often used to hold and manage UI-related state, surviving configuration changes.

## Preferred Patterns for ViewModel State

### Pattern 1: `StateFlow` from ViewModel (Generally Recommended)

This is often the most robust and flexible approach for exposing UI state from a ViewModel.

**ViewModel:**

```kotlin
class ExampleViewModel : ViewModel() {
    private val _uiState = MutableStateFlow<MyUiState>(MyUiState.Loading)
    val uiState: StateFlow<MyUiState> = _uiState.asStateFlow()

    private val _name = MutableStateFlow<String>("Initial Name")
    val name: StateFlow<String> = _name.asStateFlow()

    fun updateName(newName: String) {
        _name.value = newName
    }
    // ... other logic to update _uiState
}

data class MyUiState(...) // Can be a sealed class for complex states
```

**Composable:**

```kotlin
@Composable
fun MyScreen(viewModel: ExampleViewModel = hiltViewModel()) {
    val uiStateValue by viewModel.uiState.collectAsStateWithLifecycle()
    val nameValue by viewModel.name.collectAsStateWithLifecycle()

    // Use uiStateValue and nameValue in your UI
    Text(text = "Current name: $nameValue")
    Button(onClick = { viewModel.updateName("New Name from UI") }) {
        Text("Update Name")
    }
}
```

**When to Use:**

- For most UI state exposed from a ViewModel.
- When state logic is complex or involves asynchronous operations.
- When you might want to leverage Flow operators.
- For consistency across the app.

### Pattern 2: `mutableStateOf` in ViewModel (Direct State Exposure)

You can expose `MutableState` directly from a ViewModel, especially for simple, directly mutable UI properties.

**ViewModel:**

```kotlin
class SimpleViewModel : ViewModel() {
    var count by mutableStateOf(0)
        private set // Expose as immutable State if only ViewModel should change it

    var description by mutableStateOf("Default Description") // Writable from Composable if needed

    fun incrementCount() {
        count++
    }
}
```

**Composable:**

```kotlin
@Composable
fun AnotherScreen(viewModel: SimpleViewModel = hiltViewModel()) {
    val currentCount = viewModel.count // Direct access
    var currentDescription = viewModel.description // Direct access

    Text(text = "Count: $currentCount")
    Button(onClick = { viewModel.incrementCount() }) {
        Text("Increment")
    }

    OutlinedTextField(
        value = currentDescription,
        onValueChange = { viewModel.description = it }, // ViewModel's state is directly mutated
        label = { Text("Description") }
    )
}
```

**Important Considerations:**

- **Direct Access:** When using `var property by mutableStateOf(...)` in the ViewModel, access it _directly_ in the Composable (`val value = viewModel.property`).
- **DO NOT use `.collectAsState...()` on these properties.** This was the source of a recent error. The property itself is already a `State` object that Compose tracks.
- **Mutability:** Be mindful of exposing `var` properties that can be changed from the Composable if the logic should strictly reside in the ViewModel. Consider `private set` if only the ViewModel should modify the state. While `StateFlow` (Pattern 1) encourages a more unidirectional data flow, this pattern can be acceptable for simpler cases.

## State Within Composables

For state that is purely UI-internal to a Composable and doesn't need to be accessed by the ViewModel or survive configuration changes without specific handling (like `rememberSaveable`), use `remember { mutableStateOf(...) }`.

```kotlin
@Composable
fun MyTextField() {
    var text by remember { mutableStateOf("") }

    OutlinedTextField(
        value = text,
        onValueChange = { text = it },
        label = { Text("Enter text") }
    )
}
```

## Understanding `derivedStateOf`

`derivedStateOf` is used to create a new `State` object whose value is calculated from one or more other `State` objects. It's an optimization to ensure recomposition only happens when the _derived_ value actually changes.

```kotlin
@Composable
fun UserProfile(user: User) { // Assuming User has State properties or is itself a State
    val displayName by remember {
        derivedStateOf { "${user.firstName.value} ${user.lastName.value}" } // If firstName/lastName are State
    }
    Text("Welcome, $displayName")
}
```

Do not use `derivedStateOf` to simply wrap a single `State` object that is already directly observable, as this is unnecessary and can lead to confusion or errors if not used correctly.

## Key Takeaways & Project Consistency

1.  **Prefer `StateFlow` from ViewModels (`collectAsStateWithLifecycle`)** for a clear, unidirectional data flow and robust state management.
2.  If using **`var property by mutableStateOf(...)` in ViewModels, access it directly** in Composables (`val x = viewModel.property`).
3.  Use `remember { mutableStateOf(...) }` for local Composable UI state.
4.  Understand the purpose of tools like `derivedStateOf` and use them appropriately.
5.  **Consistency is key.** While multiple patterns can work, try to stick to the chosen primary patterns within the project to improve readability and maintainability.

## Further Reading

- [Official Compose State Documentation](https://developer.android.com/jetpack/compose/state)
- [Lifecycle-Aware State Collection](https://developer.android.com/jetpack/compose/state#state-and-jetpack-compose)
- [ViewModel and State](https://developer.android.com/jetpack/compose/state#viewmodels-source-of-truth)
