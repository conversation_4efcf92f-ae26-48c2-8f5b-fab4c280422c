# Passkey Backend Alignment Tasks

This document outlines the tasks needed to align our current passkey implementation with the backend expectations.

## Login Flow Alignment

### 1. Profile Completion Status

- [ ] Add `profileComplete` field to `LoginVerifyResponse`
- [ ] Update UI to handle profile completion status
- [ ] Add navigation flow for incomplete profiles
- [ ] Add profile completion check in session management

### 2. Session Management

- [ ] Implement proper JWT token handling
- [ ] Add token expiration tracking
- [ ] Implement token refresh mechanism
- [ ] Add secure token storage
- [ ] Add session validation on app start

### 3. Device Information

- [ ] Enhance device information in requests:
  - [ ] Add `deviceName` (Build.MODEL)
  - [ ] Add `deviceId` (Settings.Secure.ANDROID_ID)
  - [ ] Add device capabilities
  - [ ] Add device security level
- [ ] Update `LoginVerifyRequest` model
- [ ] Add device tracking for security

### 4. Error Handling

- [ ] Implement specific error codes:
  - [ ] Network errors
  - [ ] Authentication errors
  - [ ] Profile incomplete errors
  - [ ] Rate limit errors
- [ ] Add retry mechanisms
- [ ] Add user-friendly error messages
- [ ] Add error logging

## Recovery Flow Alignment

### 1. Token Management

- [ ] Add token expiration handling
- [ ] Implement token validation
- [ ] Add secure token storage
- [ ] Add token refresh mechanism

### 2. Security Features

- [ ] Implement rate limiting
- [ ] Add brute force protection
- [ ] Enhance audit logging
- [ ] Add device tracking
- [ ] Add session validation

### 3. Response Handling

- [ ] Update response models
- [ ] Add proper error handling
- [ ] Add success/failure states
- [ ] Add user feedback

## Registration Flow Alignment

### 1. Invite Token Handling

- [ ] Add invite token validation
- [ ] Add token expiration
- [ ] Add secure token storage
- [ ] Add token refresh mechanism

### 2. Profile Setup

- [ ] Add profile completion check
- [ ] Add profile setup flow
- [ ] Add profile validation
- [ ] Add profile update mechanism

### 3. Security Features

- [ ] Add rate limiting
- [ ] Add audit logging
- [ ] Add device tracking
- [ ] Add session validation

## General Improvements

### 1. Code Organization

- [ ] Create dedicated session management module
- [ ] Create dedicated security module
- [ ] Create dedicated profile management module
- [ ] Add proper documentation

### 2. Testing

- [ ] Add unit tests for new features
- [ ] Add integration tests
- [ ] Add UI tests
- [ ] Add security tests

### 3. Documentation

- [ ] Update API documentation
- [ ] Add security documentation
- [ ] Add user flow documentation
- [ ] Add error handling documentation

## Priority Order

1. Critical Security Features

   - JWT token handling
   - Rate limiting
   - Device tracking
   - Session validation

2. User Experience

   - Profile completion
   - Error handling
   - User feedback
   - Navigation flows

3. Code Quality
   - Code organization
   - Testing
   - Documentation
   - Error logging

## Notes

- All changes should maintain backward compatibility
- Security features should be implemented first
- User experience improvements should follow
- Code quality improvements should be ongoing
- Documentation should be updated as changes are made

## API Endpoint Verification

### 1. Endpoint Alignment

- [ ] Verify all endpoint paths match backend:
  - [ ] Registration endpoints:
    - [ ] `/register/challenge`
    - [ ] `/register/verify`
  - [ ] Recovery endpoints:
    - [ ] `/recover/initiate`
    - [ ] `/recover/challenge`
    - [ ] `/recover/verify`
  - [ ] Login endpoints:
    - [ ] `/login/challenge`
    - [ ] `/login/verify`
- [ ] Update any endpoints that have changed
- [ ] Add any missing endpoints
- [ ] Remove any deprecated endpoints

### 2. Request/Response Models

- [ ] Verify request/response models match backend expectations
- [ ] Update model fields to match backend
- [ ] Add any missing fields
- [ ] Remove any deprecated fields

## Reference: Current Implementation Analysis

### Current Endpoints in Backend Codebase:

1. **Registration Endpoints**:

   - ✅ `/register/challenge`
   - ✅ `/register/verify`

2. **Recovery Endpoints**:

   - ✅ `/recover/initiate`
   - ✅ `/recover/challenge`
   - ✅ `/recover/verify`

3. **Login Endpoints**:
   - ✅ `/login/challenge`
   - ✅ `/login/verify`

### Current Endpoints in Mobile Codebase:

1. **Registration Endpoints**:

   - ❌ `/auth/register/challenge` (needs update)
   - ❌ `/auth/register/verify` (needs update)

2. **Recovery Endpoints**:

   - ❌ `/auth/recovery/initiate` (needs update)
   - ❌ `/auth/recovery/register/challenge` (needs update)
   - ❌ `/auth/recovery/register/verify` (needs update)

3. **Login Endpoints**:
   - ❌ `/auth/login/challenge` (needs update)
   - ❌ `/auth/login/verify` (needs update)

### Key Differences:

1. **Base Path**:

   - Backend implementation: No `/auth/` prefix
   - Mobile implementation: Uses `/auth/` prefix
   - Action needed: Remove `/auth/` prefix from all mobile endpoints

2. **Recovery Path Structure**:
   - Backend implementation: `/recover/challenge`
   - Mobile implementation: `/auth/recovery/register/challenge`
   - Action needed: Update mobile recovery endpoints to match backend structure

### Additional Endpoints Found in Backend:

The backend codebase includes several other authentication-related endpoints that might be relevant:

- `/invite` - For invitation handling
- `/logout` - For session termination
- `/refresh-token` - For token refresh
- `/devices` - For device management
- `/azure-ad/*` - For Azure AD integration
- `/monitoring/*` - For authentication monitoring

### Required Changes:

1. Remove `/auth/` prefix from all mobile endpoints
2. Update recovery endpoint structure to match backend
3. Verify and implement any missing endpoints from the additional list
4. Update all API service interfaces in mobile code to reflect backend paths
