# Passkey Login Implementation

## User Stories

### Primary User Story

As a user of the People App,
I want to securely log in using passkeys
So that I can access my account without remembering passwords
And have a seamless, secure authentication experience

### Acceptance Criteria

1. User can initiate login by entering their email
2. System prompts for passkey authentication
3. User can authenticate using their device's biometric or security method
4. System verifies the authentication and grants access
5. User is redirected to the main app interface upon successful login
6. System handles failed authentication attempts gracefully
7. System provides clear error messages when issues occur

## Technical Implementation

### 1. UI Components

- Login screen with:
  - App logo/icon
  - Welcome message
  - Email input field
  - Sign-in button
  - Loading indicator
  - Error message display
- Biometric prompt dialog
- Error handling UI components

### 2. Authentication Flow

1. **Challenge Request**

   - Endpoint: `POST /auth/login/challenge`
   - Request body: `{ "email": "<EMAIL>" }`
   - Response: WebAuthn challenge options

2. **Passkey Authentication**

   - Use Android's WebAuthn API
   - Handle biometric prompt
   - Sign challenge with passkey
   - Generate credential response

3. **Credential Verification**
   - Endpoint: `POST /auth/login/verify`
   - Request body: Credential and email
   - Response: User ID and session token

### 3. Technical Requirements

#### Dependencies

```kotlin
dependencies {
    implementation("androidx.biometric:biometric:1.2.0-alpha05")
    implementation("com.google.android.gms:play-services-fido:18.1.0")
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
}
```

#### Configuration

```kotlin
object AuthConfig {
    const val RELYING_PARTY_ID = "peepsapp.com"
    const val API_BASE_URL = "https://api.peepsapp.com"
    const val AUTH_TIMEOUT = 60000L // 60 seconds
}
```

### 4. Security Considerations

- Secure storage of session tokens
- Proper handling of biometric authentication
- Protection against replay attacks
- Secure communication with backend
- Proper error handling without exposing sensitive information

### 5. Error Handling

- Invalid email format
- Network errors during API calls
- User cancellation of passkey prompt
- Biometric/Device lock failure
- Backend validation errors
- Timeout during challenge/authentication process

### 6. Testing Requirements

- Unit tests for login API call logic
- Integration tests covering the full flow
- UI tests for the login screen elements
- Manual testing with valid and invalid credentials
- Testing user cancellation scenarios
- Testing various network error conditions

### 7. Implementation Notes

- The `rpId` in the challenge response must match exactly with the app's configuration
- The `allowCredentials` list in the challenge response contains the credential IDs that the user has previously registered
- The `userVerification` parameter can be "required", "preferred", or "discouraged"
- The `transports` array in `allowCredentials` can include "internal" (device-bound) and "hybrid" (cross-device) options
- The `authenticatorAttachment` in the verify request indicates whether the passkey is "platform" (device-bound) or "cross-platform"
- The `os` and `browser` fields in the verify request help identify the client platform
- The session token and user ID from the verify response should be securely stored for subsequent API calls

### 8. Implementation Phases

#### Phase 1: Basic Setup

- [~] Project configuration (AuthConfig defined)
- [~] Dependencies setup (Defined in docs, not yet in build file)
- [x] Basic UI implementation (AuthScreen + AuthViewModel with mock logic)
- [ ] Network layer setup

#### Phase 2: Core Authentication

- [ ] Challenge request implementation
- [ ] Passkey authentication flow
- [ ] Credential verification
- [ ] Session management

#### Phase 3: UI/UX Polish

- [ ] Loading states (Basic implemented)
- [ ] Error handling UI (Basic implemented)
- [ ] Animations
- [ ] Accessibility improvements

#### Phase 4: Testing & Security

- [ ] Unit tests
- [ ] Integration tests
- [ ] Security audit
- [ ] Performance optimization

### 9. API Specifications

#### Login Challenge Request

```json
POST /auth/login/challenge
{
  "email": "<EMAIL>"
}
```

#### Login Challenge Response

```json
{
  "options": {
    "publicKey": {
      "challenge": "base64-encoded-challenge",
      "timeout": 60000,
      "rpId": "stage.peepsapp.ai",
      "allowCredentials": [
        {
          "type": "public-key",
          "id": "base64-encoded-credential-id",
          "transports": ["internal", "hybrid"]
        }
      ],
      "userVerification": "required"
    }
  }
}
```

#### Login Verify Request

```json
POST /auth/login/verify
{
  "credential": {
    "id": "base64-encoded-credential-id",
    "rawId": "base64-encoded-raw-id",
    "type": "public-key",
    "authenticatorAttachment": "platform",
    "response": {
      "clientDataJSON": "base64-encoded-client-data",
      "authenticatorData": "base64-encoded-authenticator-data",
      "signature": "base64-encoded-signature",
      "userHandle": "base64-encoded-user-handle"
    }
  },
  "email": "<EMAIL>",
  "os": "Android",
  "browser": "Android App"
}
```

#### Login Verify Response

```json
{
  "session_token": "jwt-session-token",
  "user_id": "user-id"
}
```

### 10. Future Enhancements

- Cross-device passkey synchronization
- Account recovery process
- Multiple passkey support
- Enhanced security features
- Analytics and monitoring
