# Passkey Registration Implementation

This document outlines the technical implementation for registering a new user via an invitation link using passkeys.

## Related Documents

- [Passkey Login Implementation](./passkey_login_implementation.md)
- [People App v0.2 PRD](../peopleapp_prd_02_mvp.md)

## User Stories

### Primary User Story

As a new user invited to the People App,
I want to securely register my account using a passkey from an invitation link
So that I can create my account without needing a password
And easily access the app using my device's security features.

### Acceptance Criteria

1. User accesses the app via a valid invitation link/token.
2. System validates the invitation token.
3. Upon valid invitation, the user is prompted to register (e.g., confirm email associated with invite).
4. User initiates passkey creation.
5. System prompts for passkey creation/registration via device biometrics or security method.
6. User successfully creates and registers a passkey.
7. System verifies the new passkey with the backend.
8. Upon successful registration, the user is logged in and potentially directed to the profile setup flow (as per AU-4).
9. System handles invalid or expired invitation tokens gracefully.
10. System provides clear error messages for registration failures (e.g., passkey creation cancelled, network error, backend verification failed).

## Technical Implementation

### 1. UI Components

- Invitation validation screen/indicator.
- [~] Registration screen (potentially pre-filled with invite email): (AuthScreen serves as initial entry)
  - Welcome message specific to registration.
  - Email display (read-only if derived from invite).
  - "Register with Passkey" button.
  - Loading indicator.
  - Error message display.
- System Passkey creation prompts (handled by Android API).
- Error handling UI components.

### 2. Registration Flow

1.  **Receive Invite Token:** The app is launched with an invitation token (e.g., via a deep link `peepsapp://invite/{token}`).
2.  **Validate Invite Token:**
    - Endpoint: `GET /auth/validate-invite/{token}`
    - Action: App calls this endpoint with the extracted token.
    - Response: Success status and potentially the associated email. Handle errors for invalid/expired tokens.
3.  **Initiate Registration:** User confirms they want to register (e.g., clicks "Register with Passkey").
4.  **Registration Challenge Request:**
    - Endpoint: `POST /auth/register/challenge`
    - Request Body: `{ "email": "user-email-from-invite" }` (or potentially the validated invite token itself).
    - Action: App requests challenge options from the backend.
    - Response: WebAuthn `publicKeyCredentialCreationOptions`.
5.  **Passkey Creation (Client-Side):**
    - Action: App uses the Android WebAuthn API (`createCredential` method of `Fido2ApiClient` or similar) with the options received from the challenge endpoint.
    - Prompts: Android system handles the UI for choosing the account and using biometrics/screen lock to authorize passkey creation.
    - Result: A `PublicKeyCredential` object containing the new passkey info.
6.  **Verify Registration:**
    - Endpoint: `POST /auth/register/verify`
    - Request Body: Contains the created `PublicKeyCredential` data (serialized, likely as JSON similar to the login verify structure but adapted for registration) and the user's email/identifier.
    - Action: App sends the new credential to the backend for verification and storage.
    - Response: Success status, potentially a new `user_id` and an initial `session_token` (logging the user in immediately).
7.  **Post-Registration:** Transition to the main app interface or the profile setup flow (AU-4).

### 3. Technical Requirements

#### Dependencies

(Likely the same as Login: Biometric, Fido, Retrofit/Gson)

```kotlin
dependencies {
    implementation("androidx.biometric:biometric:1.2.0-alpha05") // Or latest stable
    implementation("com.google.android.gms:play-services-fido:18.1.0") // Or latest stable
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    // Potentially kotlinx.serialization if preferred over Gson
}
```

#### Configuration

(Same `AuthConfig` as Login)

```kotlin
object AuthConfig {
    const val RELYING_PARTY_ID = "stage.peepsapp.ai" // Updated for Staging
    const val API_BASE_URL = "https://stage.peepsapp.ai" // Updated for Staging
    // Timeout might be relevant for registration challenge too
}
```

### 4. API Specifications

#### Validate Invite Token

```
GET /auth/validate-invite/{token}
```

- **Path Parameter:** `token` (string): The invitation token.
- **Success Response (Example):** `HTTP 200 OK` with body `{ "valid": true, "email": "<EMAIL>" }`
- **Error Response (Example):** `HTTP 404 Not Found` or `HTTP 410 Gone`

#### Registration Challenge Request

```json
POST /auth/register/challenge
{
  "email": "<EMAIL>",
  "invite_token": "invitation-token",
  "device_name": "Manufacturer Model"
}
```

#### Registration Challenge Response

```json
{
  "options": {
    "publicKey": {
      "rp": {
        "name": "Peeps App stage",
        "id": "stage.peepsapp.ai"
      },
      "user": {
        "id": "base64-encoded-user-handle",
        "name": "<EMAIL>",
        "displayName": "User Name"
      },
      "challenge": "base64-encoded-challenge",
      "pubKeyCredParams": [
        {
          "type": "public-key",
          "alg": -7
        },
        {
          "type": "public-key",
          "alg": -257
        }
      ],
      "timeout": 60000,
      "authenticatorSelection": {
        "authenticatorAttachment": "platform",
        "requireResidentKey": true,
        "userVerification": "required"
      },
      "excludeCredentials": [],
      "attestation": "none"
    }
  },
  "user_id": "new-user-id"
}
```

#### Registration Verify Request

```json
POST /auth/register/verify
{
  "credential": {
    "id": "base64-encoded-credential-id",
    "rawId": "base64-encoded-raw-id",
    "type": "public-key",
    "authenticatorAttachment": "platform",
    "response": {
      "clientDataJSON": "base64-encoded-client-data",
      "attestationObject": "base64-encoded-attestation-object"
    }
  },
  "email": "<EMAIL>",
  "invite_token": "invitation-token",
  "device_name": "Manufacturer Model"
}
```

#### Registration Verify Response

```json
{
  "session_token": "jwt-session-token",
  "person_id": "new-person-id"
}
```

### 5. Security Considerations

- Validate invite tokens server-side only
- Protect against replay attacks on challenges
- Secure handling of credential data during transmission
- Rate limiting on registration endpoints
- Ensure `relyingPartyId` matches exactly between app and backend
- Consider attestation verification level (`none`, `direct`, `indirect`) based on security needs

### 6. Error Handling

- Invalid/Expired Invite Token
- Network errors during API calls
- User cancellation of passkey creation prompt
- Biometric/Device lock failure during creation
- Backend validation errors (e.g., email already registered, invalid challenge response)
- Timeout during challenge/creation process

### 7. Testing Requirements

- Unit tests for invite token extraction and validation logic
- Unit tests for registration API call logic
- Integration tests covering the full flow (invite -> challenge -> create -> verify)
- UI tests for the registration screen elements
- Manual testing with valid and invalid invite links
- Testing user cancellation scenarios
- Testing various network error conditions

### 8. Implementation Notes

- Deep linking needs to be set up correctly in the AndroidManifest.xml to capture invite tokens
- The `rpId` in the challenge response must match exactly with the app's configuration
- The `pubKeyCredParams` array specifies supported algorithms (-7 for ES256, -257 for RS256)
- The `authenticatorSelection` parameters control the type of passkey that can be created:
  - `authenticatorAttachment: "platform"` requires device-bound passkeys
  - `requireResidentKey: true` enables discoverable credentials
  - `userVerification: "required"` ensures biometric/device lock verification
- The `attestation` parameter can be "none", "direct", or "indirect" based on security requirements
- The `device_name` field helps identify the device where the passkey is created
- The session token and person ID from the verify response should be securely stored for subsequent API calls
