name: NightlyBaselineProfiles

on:
  schedule:
    - cron:  '42 4 * * *'

jobs:
  baseline_profiles:
    name: "Generate Baseline Profiles"
    if: github.repository == 'android/nowinandroid'
    runs-on: ubuntu-latest

    permissions:
      contents: write

    timeout-minutes: 60

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Enable KVM group perms
        run: |
          echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
          sudo udevadm control --reload-rules
          sudo udevadm trigger --name-match=kvm
          ls /dev/kvm

      - name: Copy CI gradle.properties
        run: mkdir -p ~/.gradle ; cp .github/ci-gradle.properties ~/.gradle/gradle.properties

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 17

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-encryption-key: ${{ secrets.GRADLE_ENCRYPTION_KEY }}

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Accept licenses
        run: yes | sdkmanager --licenses || true

      - name: Check build-logic
        run: ./gradlew :build-logic:convention:check

      - name: Setup GMD
        run: ./gradlew :benchmarks:pixel6Api33Setup
          --info
          -Pandroid.experimental.testOptions.managedDevices.emulator.showKernelLogging=true
          -Pandroid.testoptions.manageddevices.emulator.gpu="swiftshader_indirect"

      - name: Build all build type and flavor permutations including baseline profiles
        run: ./gradlew :app:assemble
             -Pandroid.testInstrumentationRunnerArguments.androidx.benchmark.enabledRules=baselineprofile
             -Pandroid.testoptions.manageddevices.emulator.gpu="swiftshader_indirect"
             -Pandroid.experimental.testOptions.managedDevices.emulator.showKernelLogging=true
