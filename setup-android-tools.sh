#!/bin/bash

# Exit on error
set -e

echo "Detecting environment..."

# Function to find Windows username
find_windows_username() {
    # Try to find the Windows username from common mount points
    for dir in /mnt/c/Users/<USER>
        if [ -d "$dir" ] && [ "$dir" != "/mnt/c/Users/<USER>" ] && [ "$dir" != "/mnt/c/Users/<USER>" ] && [ "$dir" != "/mnt/c/Users/<USER>" ] && [ "$dir" != "/mnt/c/Users/<USER>" ] && [ "$dir" != "/mnt/c/Users/<USER>" ]; then
            echo "$dir"
            return 0
        fi
    done
    return 1
}

# Function to find Android SDK location
find_android_sdk() {
    local windows_home="$1"
    local sdk_locations=(
        "$windows_home/AppData/Local/Android/Sdk"
        "$windows_home/Android/Sdk"
    )

    for loc in "${sdk_locations[@]}"; do
        if [ -d "$loc" ]; then
            echo "$loc"
            return 0
        fi
    done
    return 1
}

# Find Windows user directory
WINDOWS_HOME=$(find_windows_username || true)
if [ -z "$WINDOWS_HOME" ]; then
    echo "Error: Could not detect Windows user directory"
    exit 1
fi
echo "Found Windows home: $WINDOWS_HOME"

# Find Android SDK
SDK_PATH=$(find_android_sdk "$WINDOWS_HOME" || true)
if [ -z "$SDK_PATH" ]; then
    echo "Error: Could not find Android SDK. Please install Android Studio and SDK first."
    exit 1
fi
echo "Found Android SDK: $SDK_PATH"

# --- Java 17 Installation/Verification ---
echo "Checking for Java 17..."

NEEDS_JAVA_INSTALL=false
if ! command -v java &>/dev/null; then
    echo "Java command not found."
    NEEDS_JAVA_INSTALL=true
else
    # Capture the full version string to check for "17." pattern
    # Redirect stderr to stdout for `java -version`
    JAVA_VERSION_OUTPUT=$(java -version 2>&1 || true)
    if echo "$JAVA_VERSION_OUTPUT" | grep -q "version \"17\\."; then # More robust check for "17."
        echo "Java 17 detected:"
        echo "$JAVA_VERSION_OUTPUT" | grep "version" # Display the detected version line
    else
        echo "Java 17 not found. Current Java version details:"
        echo "$JAVA_VERSION_OUTPUT" | grep "version" # Display the detected version line
        NEEDS_JAVA_INSTALL=true
    fi
fi

if [ "$NEEDS_JAVA_INSTALL" = true ]; then
    echo "Attempting to install OpenJDK 17..."
    # Check for apt package manager (common in WSL/Debian/Ubuntu)
    if command -v apt &>/dev/null; then
        echo "Using apt to install OpenJDK 17. This requires sudo privileges."
        # Prompt user before running sudo commands
        read -p "Do you want to proceed with 'sudo apt update && sudo apt install -y openjdk-17-jdk'? (y/N): " confirm_install
        if [[ "$confirm_install" =~ ^[Yy]$ ]]; then
            if sudo apt update && sudo apt install -y openjdk-17-jdk; then
                echo "OpenJDK 17 installation attempt finished."
                # Verify installation
                if command -v java &>/dev/null && java -version 2>&1 | grep -q "version \"17\\."; then
                    echo "Java 17 successfully installed and verified."
                    JAVA_VERSION_OUTPUT=$(java -version 2>&1 || true)
                    echo "$JAVA_VERSION_OUTPUT" | grep "version"
                else
                    echo "Error: Java 17 installation failed or is not correctly configured after install."
                    echo "Please ensure OpenJDK 17 is installed and set as the default, then re-run the script."
                    exit 1
                fi
            else
                echo "Error: 'sudo apt update && sudo apt install -y openjdk-17-jdk' failed."
                echo "Please install OpenJDK 17 manually and ensure it's the default, then re-run the script."
                exit 1
            fi
        else
            echo "OpenJDK 17 installation declined by user."
            echo "Please install OpenJDK 17 manually and ensure it's the default, then re-run the script."
            exit 1
        fi
    else
        echo "Error: 'apt' package manager not found. Cannot automatically install OpenJDK 17."
        echo "Please install OpenJDK 17 manually (e.g., using your system's package manager) and ensure it's the default, then re-run the script."
        exit 1
    fi
fi
# --- End of Java 17 Installation/Verification ---

# --- Install/Update Native ADB tools ---
echo ""
echo "Ensuring native ADB tools (android-tools-adb) are available via apt..."
if command -v apt &>/dev/null; then
    echo "Attempting to install/update android-tools-adb using apt. This requires sudo privileges."
    read -p "Do you want to proceed with 'sudo apt update && sudo apt install -y android-tools-adb'? (y/N): " confirm_adb_install
    if [[ "$confirm_adb_install" =~ ^[Yy]$ ]]; then
        if sudo apt update && sudo apt install -y android-tools-adb; then
            echo "android-tools-adb installation/update attempt finished."
            if command -v adb &>/dev/null; then
                echo "Native ADB is available at: $(which adb || true)"
                echo "Native ADB version:"
                adb version || echo "Could not retrieve native adb version."
            else
                echo "Error: Native ADB installation reported success, but ADB command is not found after install."
            fi
        else
            echo "Error: 'sudo apt update && sudo apt install -y android-tools-adb' failed."
            echo "Native ADB installation skipped."
        fi
    else
        echo "Native ADB tools installation/update declined by user."
    fi
else
    echo "Warning: 'apt' package manager not found. Cannot automatically install/update android-tools-adb."
    echo "The script will proceed to set up wrappers for ADB from your Windows Android SDK if found."
fi
# --- End of Native ADB tools ---

# Create directories for Android tools
echo "Creating tool directories..."
mkdir -p ~/Android/Sdk/platform-tools
mkdir -p ~/Android/Sdk/cmdline-tools

# Create wrapper script for adb
echo "Creating ADB wrapper..."
cat >~/Android/Sdk/platform-tools/adb <<EOF
#!/bin/bash
"$SDK_PATH/platform-tools/adb.exe" "\$@"
EOF

# Create wrapper script for fastboot
echo "Creating Fastboot wrapper..."
cat >~/Android/Sdk/platform-tools/fastboot <<EOF
#!/bin/bash
"$SDK_PATH/platform-tools/fastboot.exe" "\$@"
EOF

# Make the wrapper scripts executable
chmod +x ~/Android/Sdk/platform-tools/adb
chmod +x ~/Android/Sdk/platform-tools/fastboot

# Find Java home
# -- We will explicitly set JAVA_HOME in .bashrc later --
# JAVA_HOME_DETECTED=$(dirname $(dirname $(readlink -f $(which java))))
# echo "Using Java home: $JAVA_HOME_DETECTED"

# Set up permanent WSL paths in local.properties
# -- Let's rely on environment variables instead of local.properties for Java Home --
# echo "Configuring local.properties..."
# cat > local.properties << EOF
# # Auto-generated for WSL development
# # Generated on $(date)
# org.gradle.java.home=$JAVA_HOME_DETECTED
# sdk.dir=$SDK_PATH
# EOF

# Define expected environment variables
EXPECTED_JAVA_HOME="/usr/lib/jvm/java-17-openjdk-amd64" # Assuming standard install path
EXPECTED_ANDROID_HOME="$SDK_PATH"

RC_FILE=~/.bashrc

echo "Checking environment variables in $RC_FILE..."

# Function to add line to rc file if not present
add_to_rc_if_missing() {
    local line="$1"
    local file="$2"
    if ! grep -Fxq "$line" "$file"; then
        echo "Adding: $line"
        echo "$line" >>"$file"
    else
        echo "Already present: $line"
    fi
}

# Add JAVA_HOME setup
if [ -d "$EXPECTED_JAVA_HOME" ]; then
    add_to_rc_if_missing "" "$RC_FILE" # Add empty line for separation
    add_to_rc_if_missing "# Set JAVA_HOME for Android development" "$RC_FILE"
    add_to_rc_if_missing "export JAVA_HOME=$EXPECTED_JAVA_HOME" "$RC_FILE"
    add_to_rc_if_missing "export PATH=\$JAVA_HOME/bin:\$PATH" "$RC_FILE" # Escape $PATH
else
    echo "Warning: Expected Java 17 home not found at $EXPECTED_JAVA_HOME. Please verify installation and path."
fi

# Add Android SDK setup
if [ -d "$EXPECTED_ANDROID_HOME" ]; then
    add_to_rc_if_missing "" "$RC_FILE"
    add_to_rc_if_missing "# Android SDK Environment Setup" "$RC_FILE"
    add_to_rc_if_missing "export ANDROID_HOME=$EXPECTED_ANDROID_HOME" "$RC_FILE"
    add_to_rc_if_missing "export PATH=\$PATH:\$ANDROID_HOME/platform-tools" "$RC_FILE" # Add platform-tools
    # Optional: Add other tools if needed
    # add_to_rc_if_missing "export PATH=\$PATH:\$ANDROID_HOME/cmdline-tools/latest/bin" "$RC_FILE"
    # add_to_rc_if_missing "export PATH=\$PATH:\$ANDROID_HOME/emulator" "$RC_FILE"

    # Add ADB Server Socket export for WSL <-> Windows ADB connection
    # Preferring nameserver IP for ADB_SERVER_SOCKET as per user request.
    echo "Updating ADB_SERVER_SOCKET configuration in $RC_FILE to use nameserver IP..."
    # Remove other potential ADB_SERVER_SOCKET configurations
    sed -i '/^export ADB_SERVER_SOCKET=tcp:127\\.0\\.0\\.1:5037/d' "$RC_FILE"
    sed -i '/^export ADB_SERVER_SOCKET=tcp:\\\\\\\\:5037/d' "$RC_FILE" # Removes "export ADB_SERVER_SOCKET=tcp:\\:5037"
    # Add the nameserver version
    add_to_rc_if_missing "export ADB_SERVER_SOCKET=tcp:\\$(cat /etc/resolv.conf | grep nameserver | awk '{print \\$2}'):5037" "$RC_FILE"
    echo "Configured ADB_SERVER_SOCKET to use nameserver from /etc/resolv.conf in $RC_FILE."
    echo "The line added to $RC_FILE (if not already present) will be:"
    echo "export ADB_SERVER_SOCKET=tcp:\\$(cat /etc/resolv.conf | grep nameserver | awk '{print \\$2}'):5037"
else
    echo "Warning: Android SDK home not found at $EXPECTED_ANDROID_HOME."
fi

# Remove the old wrapper path addition if it exists (optional cleanup)
# sed -i '/# Android SDK Tools/d' $RC_FILE # Remove comment line
# sed -i '/export PATH=$PATH:$HOME\/Android\/Sdk\/platform-tools/d' $RC_FILE # Remove old path export

echo "Android development environment variable setup attempted in $RC_FILE."
echo "Please run 'source $RC_FILE' to update your current shell session."

echo "Android development environment setup complete!"
echo "Please run 'source ~/.bashrc' to update your PATH"
echo ""
echo "Verify your setup:"
echo "1. Run 'java -version' - should show Java 17"
echo "2. Run 'adb devices' - should not show any errors"
echo "3. Try building: './gradlew assembleDemoDebug'"
echo ""
echo "Next steps:"
echo "1. Launch Android Studio from Windows"
echo "2. Start an emulator"
echo "3. Run 'adb devices' to verify emulator connection"
