/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ai.peepsapp.peopleapp.navigation

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.ui.graphics.vector.ImageVector
import ai.peepsapp.peopleapp.R
import ai.peepsapp.peopleapp.core.designsystem.icon.PeepsIcons

// TODO: Confirm icon names in PeepsIcons match the actual definitions

enum class TopLevelDestination(
    @DrawableRes val selectedIcon: Int,
    @DrawableRes val unselectedIcon: Int,
    @StringRes val iconTextId: Int,
    @StringRes val titleTextId: Int,
    val route: String,
) {
    PEOPLE(
        selectedIcon = PeepsIcons.People,
        unselectedIcon = PeepsIcons.People,
        iconTextId = R.string.people,
        titleTextId = R.string.people,
        route = "people",
    ),
    PROFILE(
        selectedIcon = PeepsIcons.AccountCircle,
        unselectedIcon = PeepsIcons.AccountCircle,
        iconTextId = R.string.profile,
        titleTextId = R.string.profile,
        route = "profile",
    ),
    SETTINGS(
        selectedIcon = PeepsIcons.Settings,
        unselectedIcon = PeepsIcons.Settings,
        iconTextId = R.string.settings,
        titleTextId = R.string.settings,
        route = "settings",
    ),
    NOTIFICATIONS(
        selectedIcon = PeepsIcons.Notifications,
        unselectedIcon = PeepsIcons.Notifications,
        iconTextId = R.string.notifications,
        titleTextId = R.string.notifications,
        route = "notifications",
    ),
}
