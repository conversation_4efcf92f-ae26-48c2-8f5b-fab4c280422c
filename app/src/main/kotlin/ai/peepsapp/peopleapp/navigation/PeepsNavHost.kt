/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ai.peepsapp.peopleapp.navigation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import ai.peepsapp.peopleapp.navigation.TopLevelDestination
import ai.peepsapp.peopleapp.ui.PeepsAppState
import ai.peepsapp.peopleapp.ui.auth.AuthScreen
import ai.peepsapp.peopleapp.ui.login.LoginScreen
import ai.peepsapp.peopleapp.ui.people.PeopleScreen

object AppRoutes {
    const val AUTH_GATE = "auth_gate"
    const val AUTH_ENTRY = "auth_entry"
    const val LOGIN = "login"
    fun loginWithEmail(email: String?): String = "$LOGIN?email=$email"
}

/**
 * Top-level navigation graph. Navigation is organized as explained at
 * https://d.android.com/jetpack/compose/nav-adaptive
 *
 * The navigation graph defined in this file defines the different top level routes. Navigation
 * within each route is handled using state and Back Handlers.
 */
@Composable
fun PeepsNavHost(
    appState: PeepsAppState,
    onShowSnackbar: suspend (String, String?) -> Boolean,
    modifier: Modifier = Modifier,
) {
    val navController: NavHostController = appState.navController
    NavHost(
        navController = navController,
        startDestination = AppRoutes.AUTH_GATE,
        modifier = modifier,
    ) {
        composable(AppRoutes.AUTH_GATE) {
            LaunchedEffect(Unit) {
                navController.navigate(AppRoutes.AUTH_ENTRY) {
                    popUpTo(AppRoutes.AUTH_GATE) { inclusive = true }
                }
            }
        }

        composable(AppRoutes.AUTH_ENTRY) {
            AuthScreen(
                onNavigateToLogin = {
                    email -> navController.navigate(AppRoutes.loginWithEmail(email))
                },
                onRegistrationComplete = { sessionToken, userId ->
                    navController.navigate(TopLevelDestination.PEOPLE.route) {
                        popUpTo(AppRoutes.AUTH_ENTRY) { inclusive = true }
                        launchSingleTop = true
                    }
                }
            )
        }

        composable(
            route = "login?email={email}",
            arguments = listOf(navArgument("email") {
                type = NavType.StringType
                nullable = true
                defaultValue = null
            })
        ) {
            backStackEntry ->
            val emailArg = backStackEntry.arguments?.getString("email")
            LoginScreen(
                initialEmail = emailArg,
                onLoginSuccess = { sessionToken, userId ->
                    navController.navigate(TopLevelDestination.PEOPLE.route) {
                        popUpTo(AppRoutes.LOGIN) { inclusive = true }
                        launchSingleTop = true
                    }
                }
            )
        }

        composable(TopLevelDestination.PEOPLE.route) {
            PeopleScreen()
        }
        composable(TopLevelDestination.PROFILE.route) {
            PlaceholderScreen("Profile")
        }
        composable(TopLevelDestination.SETTINGS.route) {
            PlaceholderScreen("Settings")
        }
        composable(TopLevelDestination.NOTIFICATIONS.route) {
            PlaceholderScreen("Notifications")
        }
    }
}

@Composable
private fun PlaceholderScreen(label: String) {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Text(text = "Screen: $label")
    }
}
