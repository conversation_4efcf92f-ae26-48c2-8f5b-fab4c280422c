package ai.peepsapp.peopleapp.ui.people

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PeopleScreen(
    viewModel: PeopleViewModel = hiltViewModel()
) {
    val userData by viewModel.userData.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(title = { Text("User Details (Logged In)") })
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.Start
        ) {
            if (userData == null) {
                CircularProgressIndicator(modifier = Modifier.align(Alignment.CenterHorizontally))
                Text("Loading user data...", modifier = Modifier.align(Alignment.CenterHorizontally))
            } else {
                Text("User Logged In!", style = MaterialTheme.typography.headlineSmall)
                Spacer(modifier = Modifier.height(16.dp))

                DetailItem("User ID:", userData?.userId ?: "N/A")
                DetailItem("Session Token:", userData?.sessionToken ?: "N/A")
                
                Spacer(modifier = Modifier.height(8.dp))
                Text("Other UserData fields (if available):", style = MaterialTheme.typography.titleMedium)
                DetailItem("Theme Brand:", userData?.themeBrand?.name ?: "N/A")
                DetailItem("Dark Theme Config:", userData?.darkThemeConfig?.name ?: "N/A")
                DetailItem("Use Dynamic Color:", userData?.useDynamicColor?.toString() ?: "N/A")
                DetailItem("Should Hide Onboarding:", userData?.shouldHideOnboarding?.toString() ?: "N/A")
                // Add more fields from UserData as needed
                // DetailItem("Followed Topics:", userData?.followedTopics?.joinToString() ?: "N/A")
            }
        }
    }
}

@Composable
private fun DetailItem(label: String, value: String) {
    Column {
        Text(label, style = MaterialTheme.typography.titleSmall)
        Text(value, style = MaterialTheme.typography.bodyLarge)
    }
} 