package ai.peepsapp.peopleapp.ui.auth

import android.content.Context
import android.os.Build // Added for device name
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.credentials.CreatePublicKeyCredentialRequest
import androidx.credentials.CreatePublicKeyCredentialResponse
import androidx.credentials.CredentialManager
import androidx.credentials.exceptions.CreateCredentialException
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.peepsapp.peopleapp.core.network.model.PublicKeyCredentialCreationOptions
import ai.peepsapp.peopleapp.core.network.model.RegisterChallengeRequest
import ai.peepsapp.peopleapp.core.network.retrofit.AuthApiService
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import retrofit2.HttpException
import javax.inject.Inject
import ai.peepsapp.peopleapp.core.network.model.CreatedCredential // Added
import ai.peepsapp.peopleapp.core.network.model.RegisterVerifyRequest // Added
import ai.peepsapp.peopleapp.core.network.model.RegisterVerifyResponse // Added

// Represents the different states the auth screen can be in
sealed class AuthState {
    object Idle : AuthState() // Initial state
    object Loading : AuthState() // Checking email / requesting challenge
    data class RegistrationChallengeReceived(val options: PublicKeyCredentialCreationOptions) : AuthState()
    data class RegistrationReadyForVerification(val registrationResponseJson: String) : AuthState()
    object RegistrationInProgress : AuthState() // Added: For UI feedback during credential creation
    object Verifying : AuthState() // Added: For verification step
    data class RegistrationComplete(val sessionToken: String?, val personId: String?) : AuthState() // Added: Final success state
    object LoginChallengeReceived : AuthState() // Placeholder for login flow
    data class Error(val message: String) : AuthState()
    // Add states for verification later
}

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authApiService: AuthApiService,
    @ApplicationContext private val applicationContext: Context,
    private val json: Json
) : ViewModel() {

    private val credentialManager = CredentialManager.create(applicationContext)

    var email by mutableStateOf("")
        private set

    var authState by mutableStateOf<AuthState>(AuthState.Idle)
        private set

    // Debug states
    var debugLastRequest by mutableStateOf<String?>(null)
        private set
    var debugLastResponse by mutableStateOf<String?>(null)
        private set
    var debugLastError by mutableStateOf<String?>(null)
        private set

    fun onEmailChange(newEmail: String) {
        email = newEmail
        clearDebugInfo() // Clear debug info when email changes
        if (authState !is AuthState.Idle && authState !is AuthState.Loading) {
            authState = AuthState.Idle
        }
    }

    fun onContinueClicked() {
        clearDebugInfo()
        if (email.isBlank() || !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            authState = AuthState.Error("Please enter a valid email address.")
            debugLastError = "Validation Error: Please enter a valid email address."
            return
        }
        requestRegistrationChallenge()
    }

    private fun requestRegistrationChallenge() {
        viewModelScope.launch {
            authState = AuthState.Loading
            // Include device_name and invite_token in the request
            val deviceName = "${Build.MANUFACTURER} ${Build.MODEL}"
            // TODO: Replace dummy invite token with actual value
            val inviteToken = "dummy-invite-token"
            val request = RegisterChallengeRequest(
                email = email,
                inviteToken = inviteToken,
                deviceName = deviceName
            )
            try {
                debugLastRequest = json.encodeToString(request) // Store request
                val response = authApiService.postRegisterChallenge(request)
                // Potentially store successful response if needed for debug, or clear debug states
                // debugLastResponse = json.encodeToString(response) // Example for success
                // debugLastError = null

                response.options?.publicKey?.let { pkOptions ->
                    println("AuthViewModel: Registration challenge received for $email")
                    authState = AuthState.RegistrationChallengeReceived(pkOptions)
                } ?: run {
                    val errorMsg = "Failed to get registration options from server (publicKey was null)."
                    println("AuthViewModel: $errorMsg")
                    authState = AuthState.Error(errorMsg)
                    debugLastError = errorMsg
                    // Potentially capture response body if available even with null pkOptions
                    // debugLastResponse = "Response received but pkOptions missing or null."
                }

            } catch (e: HttpException) {
                val errorBody = e.response()?.errorBody()?.string()
                println("AuthViewModel: HttpException requesting registration challenge (${e.code()}): $errorBody")
                authState = AuthState.Error(errorBody ?: "HTTP error ${e.code()} requesting challenge.")
                debugLastError = "HTTP ${e.code()}: ${e.message()}"
                debugLastResponse = errorBody
            } catch (e: Exception) {
                println("AuthViewModel: Error requesting registration challenge: ${e.message}")
                authState = AuthState.Error(e.message ?: "An error occurred requesting challenge.")
                debugLastError = e.toString()
                debugLastResponse = null // No HTTP response body for generic exceptions
            }
        }
    }

    fun initiatePasskeyRegistration(
        activityContext: Context,
        creationOptions: PublicKeyCredentialCreationOptions
    ) {
        viewModelScope.launch {
            authState = AuthState.RegistrationInProgress
            clearDebugInfo()
            try {
                val requestJson = json.encodeToString(creationOptions)
                println("AuthViewModel: Passkey Creation Request JSON: $requestJson")
                // Storing credential request for debug, not challenge request
                // debugLastRequest = requestJson

                val createPublicKeyCredentialRequest = CreatePublicKeyCredentialRequest(requestJson)

                val result = credentialManager.createCredential(
                    context = activityContext,
                    request = createPublicKeyCredentialRequest
                )
                val publicKeyCredentialResponse = result as CreatePublicKeyCredentialResponse
                val registrationResponseJson = publicKeyCredentialResponse.registrationResponseJson
                println("AuthViewModel: Passkey registration successful. Triggering verification...")
                // Don't set state here, call verifyRegistration directly
                // authState = AuthState.RegistrationReadyForVerification(registrationResponseJson)
                verifyRegistration(registrationResponseJson) // Call verification

            } catch (e: CreateCredentialException) {
                val errorMsg = "Passkey creation failed: ${e.message} (Type: ${e.type})"
                println("AuthViewModel: Error creating passkey (${e.type}): ${e.message}")
                authState = AuthState.Error(errorMsg)
                debugLastError = errorMsg
            } catch (e: Exception) {
                val errorMsg = e.message ?: "An unexpected error occurred during passkey creation."
                println("AuthViewModel: Unexpected error during passkey registration: ${e.message}")
                authState = AuthState.Error(errorMsg)
                debugLastError = e.toString()
            }
        }
    }

    // Added function to handle verification call
    private fun verifyRegistration(registrationResponseJson: String) {
        viewModelScope.launch {
            authState = AuthState.Verifying // Set verifying state
            try {
                // Parse the FIDO response
                val credential = json.decodeFromString<CreatedCredential>(registrationResponseJson)

                // Construct the verification request
                val deviceName = "${Build.MANUFACTURER} ${Build.MODEL}"
                // TODO: Replace dummy invite token with actual value
                val inviteToken = "dummy-invite-token" 
                val request = RegisterVerifyRequest(
                    credential = credential,
                    email = email, // Use the email stored in the ViewModel
                    inviteToken = inviteToken,
                    deviceName = deviceName
                )
                debugLastRequest = json.encodeToString(request) // Store request for debug

                // Make the API call
                val response = authApiService.postRegisterVerify(request)
                debugLastResponse = json.encodeToString(response) // Store success response for debug
                debugLastError = null

                println("AuthViewModel: Registration verification successful. Token: ${response.sessionToken}, PersonID: ${response.personId}")
                // Handle success - store token? Navigate?
                authState = AuthState.RegistrationComplete(response.sessionToken, response.personId)

            } catch (e: HttpException) {
                val errorBody = e.response()?.errorBody()?.string()
                val errorMsg = errorBody ?: "HTTP error ${e.code()} during verification."
                println("AuthViewModel: HttpException verifying registration (${e.code()}): $errorBody")
                authState = AuthState.Error(errorMsg)
                debugLastError = "HTTP ${e.code()} (Verify): ${e.message()}"
                debugLastResponse = errorBody
            } catch (e: Exception) {
                val errorMsg = e.message ?: "An error occurred during verification."
                println("AuthViewModel: Error verifying registration: ${e.message}")
                authState = AuthState.Error(errorMsg)
                debugLastError = "Verify Error: ${e.toString()}"
                debugLastResponse = null
            }
        }
    }

    // TODO: Add function to handle verification call after passkey creation
    // fun verifyRegistration(registrationResponseJson: String) { ... }

    fun clearDebugInfo() {
        debugLastRequest = null
        debugLastResponse = null
        debugLastError = null
    }

    fun resetState() {
        email = ""
        authState = AuthState.Idle
        clearDebugInfo()
    }
} 