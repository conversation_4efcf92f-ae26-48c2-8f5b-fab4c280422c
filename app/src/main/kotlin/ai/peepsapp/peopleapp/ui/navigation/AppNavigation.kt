package ai.peepsapp.peopleapp.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import ai.peepsapp.peopleapp.navigation.PeepsNavHost
import ai.peepsapp.peopleapp.ui.PeepsAppState
import ai.peepsapp.peopleapp.ui.rememberPeepsAppState
import android.util.Log
import androidx.compose.foundation.layout.fillMaxSize

// AppDestinations might not be needed if PeepsNavHost handles all top-level nav
// object AppDestinations {
//    const val AUTH_ROUTE = "auth"
// }

@Composable
fun AppNavigation(appState: PeepsAppState) {
    Log.d("AppNavigation", "AppNavigation CALLED - Using PeepsNavHost")
    // val navController = rememberNavController() // PeepsNavHost manages its own controller via PeepsAppState

    // Use PeepsAppState which includes the NavController
    // val appState: PeepsAppState = rememberPeepsAppState() // Removed this line

    // Call PeepsNavHost, which now contains the auth flow and main app navigation
    PeepsNavHost(
        appState = appState,
        onShowSnackbar = { _, _ -> false }, // Provide a default or actual implementation
        modifier = Modifier.fillMaxSize()
    )

    // The old NavHost is replaced by PeepsNavHost
    // NavHost(navController = navController, startDestination = AppDestinations.AUTH_ROUTE) {
    //     composable(AppDestinations.AUTH_ROUTE) {
    //         AuthScreen() // This call was causing the error
    //     }
    // }
} 