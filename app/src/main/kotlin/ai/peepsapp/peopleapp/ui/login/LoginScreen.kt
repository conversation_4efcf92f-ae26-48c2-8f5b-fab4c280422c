package ai.peepsapp.peopleapp.ui.login

import android.app.Activity
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import android.util.Log
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import android.content.ClipData
import androidx.compose.ui.platform.Clipboard
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.platform.ClipEntry
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(
    initialEmail: String? = null,
    viewModel: LoginViewModel = hiltViewModel(),
    onLoginSuccess: (sessionToken: String, userId: String) -> Unit // Callback for successful login
) {
    val currentUiState = viewModel.uiState
    val currentEmail = viewModel.email
    val detailedError = viewModel.detailedError // Access detailed error state
    
    val snackbarHostState = remember { SnackbarHostState() }
    val localContext = LocalContext.current
    var showErrorDialog by remember { mutableStateOf(false) } // State for dialog visibility
    val clipboardManager: Clipboard = LocalClipboard.current
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(initialEmail) {
        if (initialEmail != null && currentEmail.isEmpty()) {
            viewModel.onEmailChange(initialEmail)
        }
    }

    LaunchedEffect(currentUiState) {
        val currentState = currentUiState
        Log.d("LoginScreen", "Observed uiState: $currentState")
        if (currentState is LoginState.Error) {
            // Snackbar for brief error
            snackbarHostState.showSnackbar(
                message = currentState.message,
                duration = SnackbarDuration.Short // Keep it short as details are available
            )
            // No need to automatically show the dialog, user can click the button
        } else if (currentState is LoginState.ChallengeReceived) {
            val activity = localContext as? Activity
            if (activity != null) {
                Log.d("LoginScreen", "ChallengeReceived. Attempting to get passkey assertion.")
                viewModel.getPasskeyAssertion(activity, currentState.requestJson)
            } else {
                snackbarHostState.showSnackbar(
                    message = "Error: Could not get activity context to proceed with passkey.",
                    duration = SnackbarDuration.Long
                )
            }
        } else if (currentState is LoginState.LoginSuccess) {
            Log.d("LoginScreen", "LoginSuccess. Calling onLoginSuccess callback.")
            onLoginSuccess(currentState.sessionToken, currentState.userId)
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        topBar = {
            TopAppBar(title = { Text("Login with Passkey") })
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(16.dp)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterVertically)
        ) {
            Text(
                text = "Welcome Back!",
                style = MaterialTheme.typography.headlineSmall
            )

            OutlinedTextField(
                value = currentEmail,
                onValueChange = { viewModel.onEmailChange(it) },
                label = { Text("Email") },
                singleLine = true,
                keyboardOptions = KeyboardOptions(
                    capitalization = KeyboardCapitalization.None,
                    keyboardType = KeyboardType.Email
                ),
                modifier = Modifier.fillMaxWidth(),
                enabled = currentUiState is LoginState.Idle || currentUiState is LoginState.Error
            )

            when (currentUiState) {
                is LoginState.Idle, is LoginState.Error -> {
                    Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.fillMaxWidth()) {
                        Button(
                            onClick = { viewModel.requestLoginChallenge() },
                            modifier = Modifier.fillMaxWidth(),
                            enabled = currentEmail.isNotBlank()
                        ) {
                            Text("Login with Passkey")
                        }
                        // Show button to view error details if a detailed error exists
                        if (currentUiState is LoginState.Error && detailedError != null) {
                            Spacer(modifier = Modifier.height(8.dp))
                            OutlinedButton(
                                onClick = { showErrorDialog = true },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(Icons.Filled.Info, contentDescription = "Error Details", modifier = Modifier.size(ButtonDefaults.IconSize))
                                Spacer(Modifier.size(ButtonDefaults.IconSpacing))
                                Text("View Error Details")
                            }
                        }
                    }
                }
                is LoginState.LoadingChallenge, 
                is LoginState.ProcessingPasskey, 
                is LoginState.VerifyingLogin -> {
                    CircularProgressIndicator()
                    Text(if (currentUiState is LoginState.LoadingChallenge) "Requesting login challenge..."
                         else if (currentUiState is LoginState.ProcessingPasskey) "Processing passkey..."
                         else "Verifying login...")
                }
                is LoginState.ChallengeReceived -> {
                    CircularProgressIndicator()
                    Text("Waiting for passkey selection...")
                }
                is LoginState.LoginSuccess -> {
                    Text("Login Successful! Redirecting...")
                }
            }
        }
    }

    // Error Details Dialog
    if (showErrorDialog && detailedError != null) {
        AlertDialog(
            onDismissRequest = { showErrorDialog = false },
            title = { Text("Error Details") },
            text = {
                Box(modifier = Modifier.heightIn(max = 300.dp)) { // Limit height and make scrollable
                    Text(
                        text = detailedError,
                        modifier = Modifier.verticalScroll(rememberScrollState())
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        coroutineScope.launch {
                            val clipData = ClipData.newPlainText("Login Error", detailedError)
                            clipboardManager.setClipEntry(ClipEntry(clipData))
                            snackbarHostState.showSnackbar("Error copied to clipboard", duration = SnackbarDuration.Short)
                        }
                        showErrorDialog = false
                    }
                ) {
                    Text("Copy Error")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showErrorDialog = false }
                ) {
                    Text("Dismiss")
                }
            }
        )
    }
} 