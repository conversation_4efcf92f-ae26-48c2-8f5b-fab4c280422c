package ai.peepsapp.peopleapp.ui.login

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.GetPublicKeyCredentialOption
import androidx.credentials.PublicKeyCredential
import androidx.credentials.exceptions.GetCredentialException
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.peepsapp.peopleapp.core.network.model.login.* // ktlint-disable no-wildcard-imports
import ai.peepsapp.peopleapp.core.network.retrofit.AuthApiService
import ai.peepsapp.peopleapp.core.data.repository.UserDataRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import retrofit2.HttpException
import javax.inject.Inject
import android.util.Log // Added for logging

sealed class LoginState {
    object Idle : LoginState()
    object LoadingChallenge : LoginState()
    data class ChallengeReceived(val requestJson: String) : LoginState() // Pass the GetPublicKeyCredentialOption requestJson
    object ProcessingPasskey : LoginState()
    object VerifyingLogin : LoginState()
    data class LoginSuccess(val sessionToken: String, val userId: String) : LoginState()
    data class Error(val message: String) : LoginState()
}

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val authApiService: AuthApiService,
    @ApplicationContext private val applicationContext: Context,
    private val json: Json,
    private val userDataRepository: UserDataRepository
) : ViewModel() {

    private val credentialManager = CredentialManager.create(applicationContext)

    var email by mutableStateOf("")
        private set

    var uiState by mutableStateOf<LoginState>(LoginState.Idle)
        private set

    var detailedError by mutableStateOf<String?>(null) // Added for detailed error
        private set

    // Debug states (optional, can be added if needed later like in AuthViewModel)
    // var debugLastRequest by mutableStateOf<String?>(null)
    // var debugLastResponse by mutableStateOf<String?>(null)
    // var debugLastError by mutableStateOf<String?>(null)

    fun onEmailChange(newEmail: String) {
        email = newEmail
        detailedError = null // Clear detailed error
        if (uiState !is LoginState.Idle && uiState !is LoginState.LoadingChallenge) {
            uiState = LoginState.Idle
        }
    }

    fun requestLoginChallenge() {
        if (email.isBlank() || !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            val errorMsg = "Please enter a valid email address."
            uiState = LoginState.Error(errorMsg)
            detailedError = "Validation Error: $errorMsg" // Set detailed error
            return
        }
        viewModelScope.launch {
            uiState = LoginState.LoadingChallenge
            detailedError = null // Clear previous detailed error
            try {
                val request = LoginChallengeRequest(email = email)
                // debugLastRequest = json.encodeToString(request)
                val response = authApiService.postLoginChallenge(request)

                // The response.options.publicKey already contains the challenge etc.
                // We need to build the request for credentialManager.getCredential()
                // The GetPublicKeyCredentialOption takes a request JSON string.
                // This JSON string should be structured according to the WebAuthn spec for credential requests (publicKey part).
                // The server response (`response.options.publicKey`) should directly map to this structure.
                val credentialRequestJson = json.encodeToString(response.options.publicKey)

                uiState = LoginState.ChallengeReceived(credentialRequestJson)

            } catch (e: HttpException) {
                val errorBody = e.response()?.errorBody()?.string()
                val errorMsg = errorBody ?: "HTTP error ${e.code()} requesting login challenge."
                uiState = LoginState.Error(errorMsg)
                detailedError = "HTTP ${e.code()}: ${e.message()}\nResponse: $errorBody\nStackTrace: ${e.stackTraceToString()}" // Set detailed error
                // debugLastError = "HTTP ${e.code()}: ${e.message()}" // Example debug logging
                // debugLastResponse = errorBody
            } catch (e: Exception) {
                val errorMsg = e.message ?: "An error occurred requesting login challenge."
                uiState = LoginState.Error(errorMsg)
                detailedError = e.stackTraceToString() // Set detailed error
                // debugLastError = e.toString()
                // debugLastResponse = null
            }
        }
    }

    fun getPasskeyAssertion(activityContext: Context, requestJson: String) {
        Log.d("LoginViewModel", "getPasskeyAssertion called with requestJson: $requestJson") // Added log
        viewModelScope.launch {
            Log.d("LoginViewModel", "Coroutine for getPasskeyAssertion started.") // Added this
            uiState = LoginState.ProcessingPasskey
            try {
                Log.d("LoginViewModel", "Requesting passkey assertion from credentialManager.") // Added log
                val getPublicKeyCredentialOption = GetPublicKeyCredentialOption(requestJson, clientDataHash = null)
                val getCredentialRequest = GetCredentialRequest(listOf(getPublicKeyCredentialOption))

                val result = credentialManager.getCredential(activityContext, getCredentialRequest)
                Log.d("LoginViewModel", "Passkey assertion received from credentialManager.") // Added log
                val publicKeyCredential = result.credential as PublicKeyCredential
                val assertionResponseJson = publicKeyCredential.authenticationResponseJson
                Log.d("LoginViewModel", "Extracted assertionResponseJson. Calling verifyLogin.") // Added log

                verifyLogin(assertionResponseJson)

            } catch (e: GetCredentialException) {
                Log.e("LoginViewModel", "GetCredentialException: ${e.message} (Type: ${e.type})", e) // Added log
                val errorMsg = "Passkey login failed: ${e.message} (Type: ${e.type})"
                uiState = LoginState.Error(errorMsg)
                detailedError = "GetCredentialException (Type: ${e.type}): ${e.message}\nStackTrace: ${e.stackTraceToString()}" // Set detailed error
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Exception during getPasskeyAssertion: ${e.message}", e) // Added log
                val errorMsg = e.message ?: "An unexpected error occurred during passkey login."
                uiState = LoginState.Error(errorMsg)
                detailedError = e.stackTraceToString() // Set detailed error
            } finally {
                Log.d("LoginViewModel", "Coroutine for getPasskeyAssertion finished (finally block). Current uiState: $uiState") // Added this
            }
        }
    }

    private fun verifyLogin(assertionResponseJson: String) {
        Log.d("LoginViewModel", "verifyLogin called with assertionResponseJson.") // Simplified log for brevity, can add full json if needed
        viewModelScope.launch {
            uiState = LoginState.VerifyingLogin
            try {
                // Parse the FIDO assertion response JSON
                val fidoAssertionData = json.decodeFromString<FidoAssertionResponseData>(assertionResponseJson)

                // Construct the AssertedCredential
                val assertedCredential = AssertedCredential(
                    id = fidoAssertionData.id,
                    rawId = fidoAssertionData.rawId,
                    type = fidoAssertionData.type,
                    authenticatorAttachment = fidoAssertionData.authenticatorAttachment,
                    response = AuthenticatorAssertionResponse(
                        clientDataJSON = fidoAssertionData.response.clientDataJSON,
                        authenticatorData = fidoAssertionData.response.authenticatorData,
                        signature = fidoAssertionData.response.signature,
                        userHandle = fidoAssertionData.response.userHandle
                    )
                )

                val request = LoginVerifyRequest(
                    credential = assertedCredential,
                    email = email // Use the email stored in the ViewModel
                    // os and browser have default values in the data class
                )
                // debugLastRequest = json.encodeToString(request)

                val response = authApiService.postLoginVerify(request)
                // debugLastResponse = json.encodeToString(response)

                // TODO: (Follow-up) Enhance security of sessionToken and userId storage if needed beyond current DataStore implementation.
                // Currently stored via UserDataRepository -> PeepsPreferencesDataSource.
                if (response.sessionToken != null) { // Check if sessionToken is not null (if it was made nullable as a workaround)
                    userDataRepository.updateSession(response.userId, response.sessionToken)
                    uiState = LoginState.LoginSuccess(response.sessionToken, response.userId)
                } else {
                    // This case should ideally not happen if the backend sends proper errors.
                    // If sessionToken was made nullable in LoginVerifyResponse as a temporary workaround,
                    // this branch would handle it.
                    Log.e("LoginViewModel", "Login verification successful by HTTP status, but session_token was null or not provided.")
                    val errorMsg = "Login failed: Server did not provide a session token."
                    uiState = LoginState.Error(errorMsg)
                    detailedError = "LoginVerifyResponse received with null or missing session_token. Backend might have failed verification internally without sending a proper error code."
                }

            } catch (e: HttpException) {
                val errorBody = e.response()?.errorBody()?.string()
                Log.e("LoginViewModel", "HttpException during verifyLogin: ${e.code()} - $errorBody", e) // Added log
                val errorMsg = errorBody ?: "HTTP error ${e.code()} verifying login."
                uiState = LoginState.Error(errorMsg)
                detailedError = "HTTP ${e.code()}: ${e.message()}\nResponse: $errorBody\nError Details: ${e.response()?.errorBody()?.contentType()}\nStackTrace: ${e.stackTraceToString()}" // Set detailed error
            } catch (e: Exception) {
                Log.e("LoginViewModel", "Exception during verifyLogin: ${e.message}", e) // Added log
                val errorMsg = e.message ?: "An error occurred during login verification."
                uiState = LoginState.Error(errorMsg)
                detailedError = e.stackTraceToString() // Set detailed error
            }
        }
    }

    fun resetState() {
        email = ""
        uiState = LoginState.Idle
        detailedError = null // Clear detailed error
        // clearDebugInfo() if debug vars are used
    }
} 