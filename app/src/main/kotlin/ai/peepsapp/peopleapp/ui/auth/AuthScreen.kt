package ai.peepsapp.peopleapp.ui.auth

import android.app.Activity
import android.content.ClipData
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.Clipboard
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ClipEntry
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import ai.peepsapp.peopleapp.R
import ai.peepsapp.peopleapp.feature.settings.RecoveryPasskeyRegistrationScreen
import kotlinx.coroutines.launch

@Composable
fun AuthScreen(
    viewModel: AuthViewModel = hiltViewModel(),
    onNavigateToLogin: (email: String) -> Unit,
    onRegistrationComplete: (sessionToken: String?, personId: String?) -> Unit,
) {
    val email = viewModel.email
    val authState = viewModel.authState
    val context = LocalContext.current
    val clipboardManager: Clipboard = LocalClipboard.current
    val coroutineScope = rememberCoroutineScope()

    val debugRequest = viewModel.debugLastRequest
    val debugResponse = viewModel.debugLastResponse
    val debugError = viewModel.debugLastError

    var showRecoveryScreen by remember { mutableStateOf(false) }

    if (showRecoveryScreen) {
        RecoveryPasskeyRegistrationScreen(onBackClick = { showRecoveryScreen = false })
        return
    }

    LaunchedEffect(authState) {
        when (val currentAuthState = authState) {
            is AuthState.RegistrationChallengeReceived -> {
                println("AuthScreen: Registration challenge received. Initiating passkey creation via ViewModel...")
                if (context is Activity) {
                    viewModel.initiatePasskeyRegistration(context, currentAuthState.options)
                } else {
                    println("AuthScreen: Error - Context is not an Activity. Cannot initiate passkey registration.")
                    viewModel.resetState()
                }
            }
            is AuthState.RegistrationComplete -> {
                println("AuthScreen: Registration complete. Navigating...")
                onRegistrationComplete(currentAuthState.sessionToken, currentAuthState.personId)
            }
            is AuthState.LoginChallengeReceived -> {
                 println("AuthScreen: Login challenge received (handling not implemented in AuthScreen directly).")
            }
            else -> {
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.people_app_icon___light),
                contentDescription = "App Logo",
                modifier = Modifier.size(120.dp)
            )
            Spacer(modifier = Modifier.height(32.dp))

            Text("Welcome to PeepsApp!", style = MaterialTheme.typography.titleLarge)
            Text("Enter your email to sign in or register.", style = MaterialTheme.typography.bodyMedium)
            Spacer(modifier = Modifier.height(24.dp))

            OutlinedTextField(
                value = email,
                onValueChange = { viewModel.onEmailChange(it) },
                label = { Text("Email Address") },
                singleLine = true,
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Email
                ),
                modifier = Modifier.fillMaxWidth(),
                isError = authState is AuthState.Error && authState.message.isNotEmpty(),
                readOnly = authState is AuthState.Loading ||
                           authState is AuthState.RegistrationChallengeReceived ||
                           authState is AuthState.RegistrationInProgress ||
                           authState is AuthState.Verifying
            )

            if (authState is AuthState.Error && authState.message.isNotEmpty()) {
                Text(
                    text = authState.message,
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(start = 16.dp, top = 4.dp).align(Alignment.Start)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = { viewModel.onContinueClicked() },
                enabled = authState is AuthState.Idle || authState is AuthState.Error,
                modifier = Modifier.fillMaxWidth()
            ) {
                if (authState is AuthState.Loading || authState is AuthState.RegistrationInProgress || authState is AuthState.Verifying) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = MaterialTheme.colorScheme.onPrimary,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("Register (New Account)")
                }
            }

            Button(
                onClick = { onNavigateToLogin(email) },
                enabled = email.isNotBlank() && (authState is AuthState.Idle || authState is AuthState.Error),
                modifier = Modifier.fillMaxWidth().padding(top = 8.dp)
            ) {
                Text("Login with Passkey")
            }

            Button(
                onClick = { showRecoveryScreen = true },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            ) {
                Text("Register Passkey (Recovery)")
            }

            val statusMessage = when (authState) {
                is AuthState.Loading -> "Checking email..."
                is AuthState.RegistrationChallengeReceived -> "Preparing to register..."
                is AuthState.RegistrationInProgress -> "Creating your passkey... Follow system prompts."
                is AuthState.Verifying -> "Verifying registration..."
                is AuthState.RegistrationComplete -> "Registration Successful! Redirecting..."
                else -> ""
            }
            if (statusMessage.isNotBlank()) {
                Text(statusMessage, modifier = Modifier.padding(top = 8.dp))
            }
        }

        if (debugRequest != null || debugResponse != null || debugError != null) {
            HorizontalDivider(modifier = Modifier.padding(vertical = 16.dp))
            Text("Debug Info:", style = MaterialTheme.typography.titleSmall)
            Spacer(modifier = Modifier.height(8.dp))
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 200.dp)
                    .verticalScroll(rememberScrollState())
                    .padding(bottom = 8.dp)
            ) {
                debugError?.let {
                    val errorText = it
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text("Error: $errorText", style = MaterialTheme.typography.bodySmall, modifier = Modifier.weight(1f, fill=false).padding(end=8.dp))
                        Button(
                            onClick = {
                                coroutineScope.launch {
                                    val clipData = ClipData.newPlainText("Error", errorText)
                                    clipboardManager.setClipEntry(ClipEntry(clipData))
                                }
                            },
                            modifier=Modifier.height(30.dp)
                        ) { Text("Copy Error") }
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                }
                debugRequest?.let {
                    Text("Request Body: $it", style = MaterialTheme.typography.bodySmall)
                    Spacer(modifier = Modifier.height(4.dp))
                }
                debugResponse?.let {
                    Text("Response Body: $it", style = MaterialTheme.typography.bodySmall)
                }
            }
            if (debugRequest != null || debugResponse != null) {
                Button(
                    onClick = {
                        val combinedText = buildString {
                            debugRequest?.let { append("REQUEST:\n$it\n\n") }
                            debugResponse?.let { append("RESPONSE:\n$it") }
                        }
                        coroutineScope.launch {
                            val clipData = ClipData.newPlainText("Debug Info", combinedText)
                            clipboardManager.setClipEntry(ClipEntry(clipData))
                        }
                    },
                    modifier = Modifier.padding(top = 4.dp, bottom = 4.dp)
                ) {
                    Text("Copy Request/Response")
                }
            }
            Button(onClick = { viewModel.clearDebugInfo() }) {
                Text("Clear Debug Info")
            }
        }
    }
}

/* Commenting out previews as they require ViewModel setup with Hilt or mocks
@Preview(showBackground = true)
@Composable
fun AuthScreenPreview() {
     PeepsAppTheme {
         Text("Preview requires ViewModel setup")
     }
}

@Preview(showBackground = true)
@Composable
fun AuthScreenLoadingPreview() {
    // Preview with Hilt ViewModel is complex, handle later or use ParameterProvider
}

@Preview(showBackground = true)
@Composable
fun AuthScreenErrorPreview() {
    // Preview with Hilt ViewModel is complex, handle later or use ParameterProvider
}
*/
