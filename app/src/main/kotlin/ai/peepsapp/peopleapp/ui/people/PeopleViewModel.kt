package ai.peepsapp.peopleapp.ui.people

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.peepsapp.peopleapp.core.data.repository.UserDataRepository
import ai.peepsapp.peopleapp.core.model.data.UserData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class PeopleViewModel @Inject constructor(
    userDataRepository: UserDataRepository
) : ViewModel() {
    val userData: StateFlow<UserData?> = userDataRepository.userData
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null // Or a default UserData loading state
        )
} 