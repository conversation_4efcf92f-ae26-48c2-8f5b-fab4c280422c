<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="1024dp" android:viewportHeight="1024" android:viewportWidth="1024" android:width="1024dp">
      
    <path android:fillColor="#ffffff" android:pathData="M512,0L512,0A512,512 0,0 1,1024 512L1024,512A512,512 0,0 1,512 1024L512,1024A512,512 0,0 1,0 512L0,512A512,512 0,0 1,512 0z"/>
      
    <path android:fillType="evenOdd" android:pathData="M689.7,813.1C783.2,813.1 859,737.3 859,643.8C859,586.3 830.4,535.6 786.6,505C761.5,550.2 680.9,519.6 689.7,474.5C596.2,474.5 520.4,550.3 520.4,643.8C520.4,737.3 596.2,813.1 689.7,813.1Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="732.7" android:endY="741.5" android:startX="615.6" android:startY="546.1" android:type="linear">
                        
                <item android:color="#FF5FBEFC" android:offset="0"/>
                        
                <item android:color="#FF208CFC" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M689.7,474.5C725.7,474.5 759.1,485.8 786.6,505C761.5,550.2 680.9,519.6 689.7,474.5Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="725.9" android:endY="643.8" android:startX="695.6" android:startY="461.9" android:type="linear">
                        
                <item android:color="#FF5FBEFC" android:offset="0"/>
                        
                <item android:color="#FF208CFC" android:offset="0.6"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M693.2,478.6C725.7,479.2 755.9,489.3 781.2,506.1C769.3,523.6 746.8,526.7 726.7,519.8C706.6,512.9 691.5,496.9 693.2,478.6ZM524.4,643.8C524.4,554.1 596,481 685.1,478.6C683.4,501.7 702.6,520 724.1,527.4C746.1,534.9 773.1,532.2 787.7,510.7C828.5,540.8 855,589.2 855,643.8C855,735.1 781,809.1 689.7,809.1C598.4,809.1 524.4,735.1 524.4,643.8Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="689.7" android:endY="813.1" android:startX="689.7" android:startY="474.5" android:type="linear">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M773.5,387.2m-64.8,0a64.8,64.8 0,1 1,129.7 0a64.8,64.8 0,1 1,-129.7 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="755" android:centerY="363.2" android:gradientRadius="97.3" android:type="radial">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M773.5,387.2m-60.8,0a60.8,60.8 0,1 1,121.7 0a60.8,60.8 0,1 1,-121.7 0" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="773.5" android:endY="452" android:startX="773.5" android:startY="322.4" android:type="linear">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M527.6,245.8m-64.8,0a64.8,64.8 0,1 1,129.7 0a64.8,64.8 0,1 1,-129.7 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="504.6" android:centerY="231.3" android:gradientRadius="87.5" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M527.6,245.8m-63.3,0a63.3,63.3 0,1 1,126.7 0a63.3,63.3 0,1 1,-126.7 0" android:strokeWidth="3">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="504.6" android:centerY="231.3" android:gradientRadius="87.5" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M527.6,245.8m-60.8,0a60.8,60.8 0,1 1,121.7 0a60.8,60.8 0,1 1,-121.7 0" android:strokeWidth="8">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="477.8" android:centerY="206.8" android:gradientRadius="149.9" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D846C760" android:offset="0.1"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="504.6" android:centerY="231.3" android:gradientRadius="87.5" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <group>
            
        <clip-path android:pathData="M527.6,245.8m-64.8,0a64.8,64.8 0,1 1,129.7 0a64.8,64.8 0,1 1,-129.7 0"/>
            
        <path android:pathData="M502.7,219.2l116.5,80.8l-78.5,78.2l-116.5,-80.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="510.2" android:endY="256.7" android:startX="502.7" android:startY="219.2" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M502.7,219.2l116.5,80.8l78.5,-78.2l-116.5,-80.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="548.5" android:endY="218.6" android:startX="502.7" android:startY="219.2" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M502.7,219.2l-116.5,-80.8l-78.5,78.2l116.5,80.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="457" android:endY="219.8" android:startX="502.7" android:startY="219.2" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M502.7,219.2l-116.5,-80.8l78.5,-78.2l116.5,80.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="495.2" android:endY="181.7" android:startX="502.7" android:startY="219.2" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
          
    </group>
      
    <path android:pathData="M316.9,367.4m-64.8,0a64.8,64.8 0,1 1,129.7 0a64.8,64.8 0,1 1,-129.7 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="288.8" android:centerY="341.4" android:gradientRadius="96.5" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M316.9,367.4m-60.8,0a60.8,60.8 0,1 1,121.7 0a60.8,60.8 0,1 1,-121.7 0" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="288.8" android:centerY="341.4" android:gradientRadius="96.5" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillType="evenOdd" android:pathData="M270.6,468.2H215.9C202.1,468.2 190.9,479.4 190.9,493.2V788.1C190.9,801.9 202.1,813.1 215.9,813.1H417.1C430.9,813.1 442.1,801.9 442.1,788.1V493.2C442.1,479.4 430.9,468.2 417.1,468.2H361.1L337.3,508.7C327.6,525.1 303.8,525.1 294.1,508.6L270.6,468.2Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="262" android:centerY="571.6" android:gradientRadius="235.7" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M270.6,468.2L294.1,508.6C303.8,525.1 327.6,525.1 337.3,508.7L361.1,468.2H270.6Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="290.8" android:centerY="389.3" android:gradientRadius="151" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M215.9,472.2H268.3L290.7,510.6C301.8,529.7 329.5,529.8 340.7,510.7L363.4,472.2H417.1C428.7,472.2 438.1,481.6 438.1,493.2V788.1C438.1,799.7 428.7,809.1 417.1,809.1H215.9C204.3,809.1 194.9,799.7 194.9,788.1V493.2C194.9,481.6 204.3,472.2 215.9,472.2ZM354.1,472.2L333.8,506.6C325.7,520.5 305.7,520.4 297.6,506.6L277.6,472.2H354.1Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="262" android:centerY="571.6" android:gradientRadius="235.7" android:type="radial">
                        
                <item android:color="#FFFCC625" android:offset="0"/>
                        
                <item android:color="#FFFEE658" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M364.2,814H686.6C704.2,814 716.3,796.4 710,780L549.3,362.1C541.1,340.8 510.9,340.8 502.7,362.1L340.9,780C334.6,796.4 346.7,814 364.2,814Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="455.1" android:centerY="500.4" android:gradientRadius="325" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M686.6,810H364.2C349.5,810 339.3,795.2 344.6,781.4L506.4,363.5C513.3,345.6 538.7,345.7 545.6,363.6L706.2,781.5C711.5,795.2 701.4,810 686.6,810Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="455.1" android:centerY="500.4" android:gradientRadius="325" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <group>
            
        <clip-path android:pathData="M364.2,814H686.6C704.2,814 716.3,796.4 710,780L549.3,362.1C541.1,340.8 510.9,340.8 502.7,362.1L340.9,780C334.6,796.4 346.7,814 364.2,814Z"/>
            
        <path android:pathData="M475.7,503.8l330.2,135.1l-133.7,327.3l-330.2,-135.1z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="521.5" android:endY="613.2" android:startX="475.7" android:startY="503.8" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M475.7,503.8l330.2,135.1l133.7,-327.3l-330.2,-135.1z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="585" android:endY="457.8" android:startX="475.7" android:startY="503.8" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M475.7,503.8l-330.2,-135.1l-133.7,327.3l330.2,135.1z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="366.3" android:endY="549.7" android:startX="475.7" android:startY="503.8" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M475.7,503.8l-330.2,-135.1l133.7,-327.3l330.2,135.1z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="429.8" android:endY="394.3" android:startX="475.7" android:startY="503.8" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
          
    </group>
    
</vector>
