<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <debug-overrides>
        <trust-anchors>
            <!-- Trust user-added CAs while debuggable. -->
            <certificates src="user" />
            <!-- Trust our custom mkcert CA -->
            <certificates src="@raw/mkcert_root_ca" />
        </trust-anchors>
    </debug-overrides>
    <!-- You can also define domain-specific configurations if needed -->
    <!--
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">local.peepsapp.ai</domain>
        <trust-anchors>
            <certificates src="@raw/mkcert_root_ca"/>
        </trust-anchors>
    </domain-config>
    -->
</network-security-config>