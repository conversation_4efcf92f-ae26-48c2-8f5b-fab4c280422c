Draft your initial prompt or ideas for a project here. Use this to then kickstart the project with the cursor agent mode when using the agile workflow, documented in docs/agile-readme.md. After the initial prd is drafted, work with the LLM in cursor or with an external LLM to ask questions, have the LLM ask you questions, etc., to really define an adequate prd and story list. Then continue with generating of the architecture document to ensure the project is built in a way that is easy to maintain and scale as you need it to be, along with a clear specification of what technologies and libraries you want to use. This will also help you figure out what rules you might want to initiall generate to help you build the project.

Example:

@agile <or rely on the rules with the workflow to be in place if not using notepads> Lets built a nextJs 15 web app to track our monthly income and expenses. I want a modern UI, secure storage in supabase, and a modern API. Etc...
