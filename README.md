# PeepsApp

So, you want to get PeepsApp up and running? Let's dive in. This project started life as a fork of [Now in Android](https://github.com/android/nowinandroid), so you'll see some of that DNA in its structure.

## Getting Your Dev Environment Ready

First things first, you'll need some tools.

### Core Setup (Windows-Side)

- **Android Studio:** Grab the latest stable version (Giraffe or newer is a good bet).
  - It manages its own Gradle JDK, but it's worth double-checking it's set to JDK 17. You can find this under `File > Settings > Build, Execution, Deployment > Build Tools > Gradle > Gradle JDK`.
- **Android SDK:** You'll configure this through Android Studio's SDK Manager (`File > Settings > Appearance & Behavior > System Settings > Android SDK`). Make sure you've got these bits:
  - **SDK Platforms:** Android API 35 (or whatever `compileSdk` is currently set to in the project).
  - **SDK Tools:**
    - Android SDK Build-Tools (latest 35.x.x, matching `compileSdk`).
    - Android SDK Platform-Tools (this gives you `adb`).
    - Android SDK Command-line Tools (latest, for `sdkmanager`).
    - Android Emulator (if you're not using a physical device).
    - (Optional but recommended for emulator speed) Android Emulator hypervisor driver.
    - (Optional) System images for your emulators (e.g., Google APIs Intel x86_64 Atom System Image for API 35).
- **ADB (Android Debug Bridge):** This comes with the Platform-Tools. You'll want `adb` in your Windows `PATH` so you can run it from anywhere. It's usually at `%LOCALAPPDATA%\Android\Sdk\platform-tools`.
  - _Quick note:_ Even if you run `adb` commands from WSL later, the actual ADB server that talks to your devices/emulators is running on Windows.

### Verifying Your Android SDK Components

If you want to double-check what SDK components Android Studio _thinks_ are installed, you can use the `sdkmanager` command-line tool. Since `sdkmanager.bat` is a Windows script, you'll run it from a Windows Command Prompt or PowerShell.

We've got a helper script in the project root for this: `run_sdkmanager.bat`.
Just run it from a PowerShell terminal:

```cmd
REM Navigate to your project directory first if you're not already there
cd path\to\peepsapp-android-compose
.\run_sdkmanager.bat
```

This script temporarily sets up the `JAVA_HOME` to point to Android Studio's bundled Java runtime, which `sdkmanager.bat` needs.

### Cloning the Project

Pretty standard stuff:

```bash
git clone https://github.com/Positplace/peepsapp-android-compose.git
cd peepsapp-android-compose
```

## WSL for Command-Line Magic

We're aiming for a setup where you can use the WSL command line for building and deploying, while Android Studio on Windows handles the emulator.

1.  **Install OpenJDK 17 in WSL:**
    Android Studio has its own Java, but for running Gradle commands (`./gradlew`) directly in WSL, you need a JDK there too.

    ```bash
    sudo apt update
    sudo apt install openjdk-17-jdk -y
    ```

    Check it: `java -version` should show OpenJDK 17.

2.  **Set up `JAVA_HOME` in WSL:**
    Add this to your WSL shell's config file (like `~/.bashrc` or `~/.zshrc`):

    ```bash
    export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
    export PATH=$JAVA_HOME/bin:$PATH
    ```

    Then, reload your shell's config: `source ~/.bashrc` (or `source ~/.zshrc`).

3.  **Link Windows Android SDK to WSL:**
    The `setup-android-tools.sh` script in the project root helps with this. It sets up `ANDROID_HOME` in WSL to point to your Windows Android SDK installation.

    ```bash
    chmod +x setup-android-tools.sh
    ./setup-android-tools.sh
    source ~/.bashrc # Or ~/.zshrc
    ```

4.  **Quick Sanity Check in WSL:**
    ```bash
    echo $JAVA_HOME      # Should be your WSL JDK path
    java -version       # Should be OpenJDK 17
    echo $ANDROID_HOME  # Should be like /mnt/c/... pointing to your Windows SDK
    adb devices         # Should list devices/emulators (make sure ADB server is running on Windows)
    ```

## Building and Running the App

With the setup done, here's how you typically build and run from WSL:

```bash
# Build the demoDebug variant
./gradlew assembleDemoDebug

# Install it on your connected device or running emulator
adb install -r app/build/outputs/apk/demo/debug/app-demo-debug.apk
```

## How This App is Put Together (Architecture & Structure)

This app aims for a Clean Architecture approach using MVVM for the UI layer. Here's a peek at the directory structure:

```
app/
├── data/              # Repositories, local/remote data sources, data models
├── di/                # Hilt dependency injection modules
├── domain/            # Use cases, domain models, core business logic
├── ui/                # Jetpack Compose UI, ViewModels, UI state
│   ├── components/    # Reusable bits and pieces for your UI
│   ├── screens/       # The main screens of the app
│   ├── theme/         # Colors, typography, all that jazz
│   └── navigation/    # Navigation graph and related components
├── utils/             # Helper utilities
└── services/          # Higher-level services orchestrating domain logic (if distinct from use cases)
```

## Key Tech & Libraries

- **Language:** Kotlin (of course!)
- **UI:** Jetpack Compose
- **Architecture:** MVVM with Clean Architecture vibes
- **Dependency Injection:** Hilt
- **Networking:** Retrofit & OkHttp (with a logging interceptor for peeking at traffic), using Kotlinx Serialization for JSON.
- **Authentication:** Passkeys, powered by AndroidX Credential Manager.
- **Navigation:** Jetpack Navigation Compose.
- **Async:** Kotlin Coroutines & Flow.
- **Images:** Coil.
- **Build:** Gradle with the Kotlin DSL.
- **Testing:** JUnit4 for unit tests, AndroidX Test libraries for others.
- **Min API:** Android API 24+ (Android 7.0 Nougat).

## Common Dev Workflows

### Building & Deploying

(Same as "Building and Running the App" section above)

```bash
./gradlew assembleDemoDebug --info # For a more verbose build
adb install -r app/build/outputs/apk/demo/debug/app-demo-debug.apk
```

### Running Tests

```bash
# Unit tests
./gradlew test

# Instrumented tests (needs a connected device/emulator)
./gradlew connectedAndroidTest

# UI tests (often part of connectedAndroidTest or a specific task)
# ./gradlew connectedCheck # Or similar, check project tasks
```

### Performance Stuff

```bash
# If you're using baseline profiles (good idea!)
# Example command, adjust for your target device/API:
./gradlew :app:pixel6Api33BenchmarkAndroidTest

# To see what the Compose compiler is up to:
./gradlew assembleRelease -PenableComposeCompilerMetrics=true -PenableComposeCompilerReports=true
```

## Build Variants Available

- `demoDebug`: Your main go-to for development, with logging enabled.

## Adding New Features (The Gist)

1.  Create a new feature package, probably under `app/src/main/kotlin/ai/peepsapp/peopleapp/feature/yourfeaturename`.
2.  Implement the necessary bits in `data`, `domain`, and `ui` layers for your feature.
3.  Wire it into the navigation graph (likely in `PeepsNavHost.kt` or a feature-specific navigation file).
4.  Write some tests!

## Debugging Tips

- Android Studio's logcat and layout inspector is your friend.
- Android Studio's profilers for performance investigations.

## Troubleshooting the WSL Setup

1.  **ADB Not Connecting?**

    - Is your emulator/device actually running and visible to Windows?
    - `adb devices` in WSL should list it. If not, try `adb kill-server && adb start-server` (this needs to affect the Windows ADB server; you might need to run it in a Windows CMD/PowerShell, or ensure your WSL's `adb` is configured to talk to the Windows server, which `setup-android-tools.sh` aims to help with).

2.  **Builds Failing in WSL?**

    - `java -version` in WSL: Still OpenJDK 17?
    - `echo $JAVA_HOME`: Pointing to your WSL JDK?
    - `echo $ANDROID_HOME`: Pointing to `/mnt/c/...` (your Windows SDK path)? Your `local.properties` file (if it exists and is tracked, which is sometimes not recommended) or this env var tells Gradle where to find the SDK.

3.  **WSL Performance Tips:**
    - Keep Android Studio running on the Windows side, especially for the emulator. It's generally smoother.
    * Run Gradle builds (`./gradlew ...`) and Git commands inside WSL – it's usually much faster with the Linux filesystem.

## License

Proprietary - All rights reserved

---

This project started as a fork of [Now in Android](https://github.com/android/nowinandroid)
