# Copyright (C) 2022 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# GOOGLE SAMPLE PACKAGING DATA
#
# This file is used by Google as part of our samples packaging process.
# End users may safely ignore this file. It has no relevance to other systems.
---
status:       PUBLISHED
technologies: [Android, JetpackCompose, Coroutines]
categories:
  - AndroidTesting
  - AndroidArchitecture
  - AndroidArchitectureUILayer
  - AndroidArchitectureDomainLayer
  - AndroidArchitectureDataLayer
  - AndroidArchitectureStateProduction
  - AndroidArchitectureStateHolder
  - JetpackComposeTesting
  - JetpackComposeA11y
  - JetpackComposeArchitectureAndState
  - JetpackComposeDesignSystems
  - JetpackComposeNavigation
  - JetpackComposeAnimation
solutions:
  - Mobile
  - Flow
  - JetpackHilt
  - JetpackDataStore
  - JetpackRoom
  - JetpackNavigation
  - JetpackWorkManager
  - JetpackLifecycle
languages:    [Kotlin]
github:       android/nowinandroid
level:        ADVANCED
license: apache2
