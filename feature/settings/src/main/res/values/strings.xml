<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright 2022 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <string name="feature_settings_top_app_bar_action_icon_description">Settings</string>
    <string name="feature_settings_top_app_bar_navigation_icon_description">Search</string>
    <string name="feature_settings_title">Settings</string>
    <string name="feature_settings_loading">Loading…</string>
    <string name="feature_settings_privacy_policy">Privacy policy</string>
    <string name="feature_settings_licenses">Licenses</string>
    <string name="feature_settings_brand_guidelines">Brand Guidelines</string>
    <string name="feature_settings_feedback">Feedback</string>
    <string name="feature_settings_theme">Theme</string>
    <string name="feature_settings_brand_default">Default</string>
    <string name="feature_settings_brand_android">Android</string>
    <string name="feature_settings_dark_mode_preference">Dark mode preference</string>
    <string name="feature_settings_dark_mode_config_system_default">System default</string>
    <string name="feature_settings_dark_mode_config_light">Light</string>
    <string name="feature_settings_dark_mode_config_dark">Dark</string>
    <string name="feature_settings_dynamic_color_preference">Use Dynamic Color</string>
    <string name="feature_settings_dynamic_color_yes">Yes</string>
    <string name="feature_settings_dynamic_color_no">No</string>
    <string name="feature_settings_dismiss_dialog_button_text">OK</string>
    <string name="debug_information_title">Debug Information</string>
    <string name="app_sha256_fingerprint">App SHA-256: %1$s</string>
    <string name="copy_fingerprint_content_description">Copy fingerprint</string>
</resources>
