// app/src/main/kotlin/ai/peepsapp/peopleapp/feature/settings/RecoveryViewModel.kt
package ai.peepsapp.peopleapp.feature.settings

import android.content.Context
import android.os.Build
// import android.os.Build // Not used
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.credentials.CreatePublicKeyCredentialRequest
import androidx.credentials.CreatePublicKeyCredentialResponse
import androidx.credentials.CredentialManager
import androidx.credentials.exceptions.CreateCredentialException
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.peepsapp.peopleapp.core.common.util.Base64UrlUtil
import ai.peepsapp.peopleapp.core.network.model.recovery.* // Imports AttestationResponseForVerify and its nested ClientDataAndAttestationObjectForVerify
import ai.peepsapp.peopleapp.core.network.retrofit.AuthApiService

// FIDO2 GMS Core types
import com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialCreationOptions as FidoPublicKeyCredentialCreationOptions
import com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialRpEntity
import com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialUserEntity
import com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialParameters
import com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialDescriptor
import com.google.android.gms.fido.fido2.api.common.AuthenticatorSelectionCriteria
import com.google.android.gms.fido.fido2.api.common.ResidentKeyRequirement
import com.google.android.gms.fido.fido2.api.common.UserVerificationRequirement
import com.google.android.gms.fido.fido2.api.common.AttestationConveyancePreference
import com.google.android.gms.fido.fido2.api.common.Attachment
import com.google.android.gms.fido.fido2.api.common.PublicKeyCredential as FidoPublicKeyCredential 
import com.google.android.gms.fido.fido2.api.common.AuthenticatorAttestationResponse as FidoAuthenticatorAttestationResponse
import com.google.android.gms.fido.common.Transport // Added this import

import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import retrofit2.HttpException
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

sealed class RecoveryState {
    object Idle : RecoveryState()
    object LoadingInitiate : RecoveryState()
    data class InitiateSuccess(val token: String, val personId: String) : RecoveryState()
    object LoadingChallenge : RecoveryState()
    data class ChallengeSuccess(val challengeData: ServerChallengeData) : RecoveryState()
    object RegistrationInProgress : RecoveryState()
    object VerifyingPasskey : RecoveryState()
    data class RecoveryComplete(
        val message: String,
        val sessionToken: String? = null,
        val personId: String? = null
    ) : RecoveryState()
    data class Error(val message: String) : RecoveryState()
}

@HiltViewModel
open class RecoveryViewModel @Inject constructor(
    private val authApiService: AuthApiService,
    @ApplicationContext private val applicationContext: Context,
    private val json: Json
) : ViewModel() {

    // Publicly exposed state
    private val _uiState = MutableStateFlow<RecoveryState>(RecoveryState.Idle)
    open val uiState: StateFlow<RecoveryState> = _uiState.asStateFlow()

    var email: String by mutableStateOf("")
        private set

    var debugInfo: String by mutableStateOf("")
        private set

    var currentRecoveryToken: String? by mutableStateOf(null)
        private set

    private val credentialManager by lazy {
        CredentialManager.create(applicationContext)
    }

    fun onEmailChange(newEmail: String) {
        email = newEmail
    }

    open fun initiateRecovery() {
        if (email.isBlank()) {
            _uiState.value = RecoveryState.Error("Email cannot be empty.")
            return
        }
        viewModelScope.launch {
            _uiState.value = RecoveryState.LoadingInitiate
            debugInfo = "Initiating recovery for $email...\n"
            try {
                val request = InitiateRecoveryRequest(email)
                val response = authApiService.postInitiateRecovery(request)
                currentRecoveryToken = response.token
                _uiState.value = RecoveryState.InitiateSuccess(response.token, response.personId)
                debugInfo += "Initiate success. Token: ${response.token}, PersonID: ${response.personId}\n"
            } catch (e: HttpException) {
                handleApiError(e, "Initiation failed")
            } catch (e: Exception) {
                handleGenericError(e, "Initiation failed")
            }
        }
    }

    open fun createRegistrationChallenge() {
        val token = currentRecoveryToken ?: return
        viewModelScope.launch {
            _uiState.value = RecoveryState.LoadingChallenge
            debugInfo += "Requesting recovery registration challenge for token $token...\n"
            try {
                val request = RecoveryChallengeRequest(token = token, isRecovery = true)
                val serverChallengeContainer = authApiService.postRecoveryRegisterChallenge(request)
                _uiState.value = RecoveryState.ChallengeSuccess(serverChallengeContainer.options.publicKeyData)
                debugInfo += "Challenge received. RP ID: ${serverChallengeContainer.options.publicKeyData.rp.id}, server user_id: ${serverChallengeContainer.userId}\n"
            } catch (e: HttpException) {
                handleApiError(e, "Challenge creation failed")
            } catch (e: Exception) {
                handleGenericError(e, "Challenge creation failed")
            }
        }
    }

    open fun registerPasskeyWithDevice(activityContext: Context, challengeData: ServerChallengeData) {
        viewModelScope.launch {
            _uiState.value = RecoveryState.RegistrationInProgress
            debugInfo += "Launching FIDO2 registration intent...\n"
            try {
                val requestJson = json.encodeToString(challengeData)
                debugInfo += "FIDO2 Challenge Options (requestJson): $requestJson\n"
                val createPublicKeyCredentialRequest = CreatePublicKeyCredentialRequest(requestJson)

                val result = credentialManager.createCredential(
                    context = activityContext,
                    request = createPublicKeyCredentialRequest
                )
                val publicKeyCredentialResponse = result as CreatePublicKeyCredentialResponse
                verifyPasskeyAttestation(publicKeyCredentialResponse.registrationResponseJson)

            } catch (e: CreateCredentialException) {
                val errorMsg = "Passkey creation failed: ${e.message} (Type: ${e.type})"
                _uiState.value = RecoveryState.Error(errorMsg)
                debugInfo += "Error creating passkey: $errorMsg\n"
            } catch (e: Exception) {
                val errorMsg = e.message ?: "An unexpected error occurred during passkey creation."
                _uiState.value = RecoveryState.Error(errorMsg)
                debugInfo += "Unexpected error during passkey registration: $errorMsg\n"
            }
        }
    }

    private fun verifyPasskeyAttestation(registrationResponseJson: String) {
        val token = currentRecoveryToken ?: run {
            _uiState.value = RecoveryState.Error("Recovery token missing for verification.")
            debugInfo += "Error: Recovery token missing for verification.\n"
            return
        }
        viewModelScope.launch {
            _uiState.value = RecoveryState.VerifyingPasskey
            debugInfo += "Verifying passkey attestation...\n"
            try {
                // Deserialize the registrationResponseJson into our new @Serializable data class
                val fidoRegData = json.decodeFromString<FidoRegistrationResponseData>(registrationResponseJson)

                // No need to Base64Url encode rawId, clientDataJSON, attestationObject again,
                // as they are already in that format in FidoRegistrationResponseData from the authenticator's JSON.
                val attestationForVerify = AttestationResponseForVerify(
                    id = fidoRegData.id,
                    rawId = fidoRegData.rawId, // Already Base64Url encoded by authenticator
                    response = AttestationResponseForVerify.ClientDataAndAttestationObjectForVerify(
                        clientDataJSON = fidoRegData.response.clientDataJSON, // Already Base64Url encoded
                        attestationObject = fidoRegData.response.attestationObject // Already Base64Url encoded
                    ),
                    type = fidoRegData.type,
                    authenticatorAttachment = fidoRegData.authenticatorAttachment
                )

                val deviceName = Build.MODEL // Get device name
                val request = VerifyRecoveryRequest(
                    token = token, 
                    credential = attestationForVerify,
                    deviceName = deviceName // Populate device_name
                )
                // Log the raw response before deserialization
                val httpResponse = authApiService.postRecoveryRegisterVerifyRaw(request) // Assuming a raw call method
                val rawJsonResponse = httpResponse.body()?.string() // Or .string() depending on ResponseBody type
                debugInfo += "Raw verification response JSON: $rawJsonResponse\n"

                if (!httpResponse.isSuccessful || rawJsonResponse == null) {
                    val errorBody = httpResponse.errorBody()?.string() ?: "Unknown error body"
                    val errorMsg = "Passkey verification failed: HTTP ${httpResponse.code()} - $errorBody"
                    _uiState.value = RecoveryState.Error(errorMsg)
                    debugInfo += "Error: $errorMsg\n"
                    return@launch
                }

                // Now deserialize the logged rawJsonResponse
                val response = json.decodeFromString<VerifyRecoveryResponse>(rawJsonResponse)

                _uiState.value = RecoveryState.RecoveryComplete(
                    message = response.message ?: "Recovery successful!",
                    sessionToken = response.sessionToken,
                    personId = response.personId
                )
                debugInfo += "Passkey recovery verification successful: ${response.success} - ${response.message}. Session: ${response.sessionToken != null}, PersonID: ${response.personId}\n"

            } catch (e: HttpException) {
                handleApiError(e, "Passkey verification failed")
            } catch (e: Exception) {
                handleGenericError(e, "Passkey verification failed")
            }
        }
    }

    private fun convertToServerChallengeToFidoOptions(serverChallenge: ServerChallengeData): FidoPublicKeyCredentialCreationOptions {
        val rp = com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialRpEntity(
            serverChallenge.rp.id,
            serverChallenge.rp.name,
            null
        )
        val user = com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialUserEntity(
            Base64UrlUtil.decode(serverChallenge.user.id),
            serverChallenge.user.name,
            null,
            serverChallenge.user.displayName
        )
        val challenge = Base64UrlUtil.decode(serverChallenge.challenge)
        val pubKeyCredParams = serverChallenge.pubKeyCredParams.map {
            com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialParameters(it.type, it.alg)
        }
        val excludeCredentials = serverChallenge.excludeCredentials?.map { cred ->
            com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialDescriptor(
                cred.type,
                Base64UrlUtil.decode(cred.id),
                cred.transports?.mapNotNull { transportName ->
                    try { com.google.android.gms.fido.common.Transport.valueOf(transportName.uppercase().replace("-","_")) } catch (e: IllegalArgumentException) { null }
                }
            )
        }
        val authenticatorSelectionCriteria = serverChallenge.authenticatorSelection?.let {
            val builder = com.google.android.gms.fido.fido2.api.common.AuthenticatorSelectionCriteria.Builder()
            it.authenticatorAttachment?.let { att -> 
                try { builder.setAttachment(com.google.android.gms.fido.fido2.api.common.Attachment.valueOf(att.uppercase().replace("-","_"))) } catch (e: IllegalArgumentException) { /* attachment was invalid, do nothing or log */ }
            }
            builder.setResidentKeyRequirement(it.residentKey?.let { rk -> try { com.google.android.gms.fido.fido2.api.common.ResidentKeyRequirement.valueOf(rk.uppercase()) } catch (e: IllegalArgumentException) { null } } ?: it.requireResidentKey?.let { if(it) com.google.android.gms.fido.fido2.api.common.ResidentKeyRequirement.RESIDENT_KEY_REQUIRED else com.google.android.gms.fido.fido2.api.common.ResidentKeyRequirement.RESIDENT_KEY_DISCOURAGED } )
            builder.build()
        }

        val optionsBuilder = FidoPublicKeyCredentialCreationOptions.Builder()
            .setRp(rp)
            .setUser(user)
            .setChallenge(challenge)
            .setParameters(pubKeyCredParams)

        serverChallenge.timeout?.let { optionsBuilder.setTimeoutSeconds(it.toDouble() / 1000.0) }
        excludeCredentials?.let { optionsBuilder.setExcludeList(it) }
        authenticatorSelectionCriteria?.let { optionsBuilder.setAuthenticatorSelection(it) }
        serverChallenge.attestation?.let { pref -> try { optionsBuilder.setAttestationConveyancePreference(com.google.android.gms.fido.fido2.api.common.AttestationConveyancePreference.valueOf(pref.uppercase())) } catch (e: IllegalArgumentException) { /* default or log */ }}

        return optionsBuilder.build()
    }

    private fun handleApiError(e: HttpException, contextMessage: String) {
        val errorBody = e.response()?.errorBody()?.string()
        val detailedMessage = errorBody ?: e.message()
        val errorMsg = "$contextMessage: HTTP ${e.code()} - $detailedMessage"
        _uiState.value = RecoveryState.Error(errorMsg)
        debugInfo += "Error: $errorMsg\nResponse Body for more details: $errorBody\n"
    }

    private fun handleGenericError(e: Exception, contextMessage: String) {
        val errorMsg = "$contextMessage: ${e.message ?: "An unknown error occurred."}"
        _uiState.value = RecoveryState.Error(errorMsg)
        debugInfo += "Error: $errorMsg\nException: ${e.toString()}\n"
    }

    fun resetState() {
        email = ""
        currentRecoveryToken = null
        _uiState.value = RecoveryState.Idle
        debugInfo = ""
    }
} 