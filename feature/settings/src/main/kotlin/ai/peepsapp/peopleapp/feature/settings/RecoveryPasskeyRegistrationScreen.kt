package ai.peepsapp.peopleapp.feature.settings

import android.app.Activity
import android.content.Context
import android.content.ClipData
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboard
import androidx.compose.ui.platform.Clipboard
import androidx.compose.ui.platform.ClipEntry
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import ai.peepsapp.peopleapp.feature.settings.RecoveryViewModel
import ai.peepsapp.peopleapp.feature.settings.RecoveryState
import kotlinx.coroutines.flow.MutableStateFlow
import com.google.android.gms.fido.fido2.api.common.PublicKeyCredentialCreationOptions as FidoPublicKeyCredentialCreationOptions
import kotlinx.coroutines.launch
import androidx.compose.ui.platform.LocalContext
import ai.peepsapp.peopleapp.core.network.model.recovery.ServerChallengeData
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecoveryPasskeyRegistrationScreen(
    viewModel: RecoveryViewModel = hiltViewModel(),
    onBackClick: () -> Unit,
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var email by remember { mutableStateOf("") }
    val clipboard: Clipboard = LocalClipboard.current
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }

    LaunchedEffect(uiState) {
        val currentState = uiState
        if (currentState is RecoveryState.RecoveryComplete) {
            val fullMessage = StringBuilder(currentState.message)
            if (currentState.sessionToken != null) {
                fullMessage.append("\nSession is active.")
            }
            snackbarHostState.showSnackbar(
                message = fullMessage.toString(),
                duration = androidx.compose.material3.SnackbarDuration.Short
            )
            onBackClick()
        } else if (currentState is RecoveryState.Error) {
            snackbarHostState.showSnackbar(
                message = currentState.message,
                duration = androidx.compose.material3.SnackbarDuration.Long
            )
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        topBar = {
            TopAppBar(
                title = { Text("Recovery Passkey Registration") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(16.dp)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedTextField(
                value = email,
                onValueChange = {
                    email = it
                    viewModel.onEmailChange(it)
                },
                label = { Text("Email for Recovery") },
                singleLine = true,
                keyboardOptions = KeyboardOptions(capitalization = KeyboardCapitalization.None)
            )

            Button(onClick = { viewModel.initiateRecovery() }) {
                Text("1. Initiate Recovery")
            }

            Button(onClick = { viewModel.createRegistrationChallenge() }) {
                Text("2. Register Passkey (Challenge)")
            }

            val currentUiState = uiState
            if (currentUiState is RecoveryState.ChallengeSuccess) {
                val activity = LocalContext.current as? Activity
                Button(
                    onClick = {
                        activity?.let { act ->
                            viewModel.registerPasskeyWithDevice(act, currentUiState.challengeData)
                        }
                    },
                    enabled = activity != null
                ) {
                    Text("3. Create Passkey on Device")
                }
            }

            Text(
                text = "Debug Info:",
                style = MaterialTheme.typography.titleMedium
            )
            SelectionContainer {
                Text(
                    text = viewModel.debugInfo,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 100.dp, max = 300.dp)
                        .border(1.dp, MaterialTheme.colorScheme.outline)
                        .padding(8.dp)
                        .verticalScroll(rememberScrollState())
                )
            }
            Button(
                onClick = { 
                    coroutineScope.launch {
                        val annotatedString = AnnotatedString(viewModel.debugInfo)
                        val clipData = ClipData.newPlainText("debug_info", annotatedString.text)
                        clipboard.setClipEntry(ClipEntry(clipData))
                    }
                },
                enabled = viewModel.debugInfo.isNotBlank()
            ) {
                Text("Copy Debug Info")
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun PreviewRecoveryPasskeyRegistrationScreen() {
    // Dummy ViewModel for preview
    val dummyViewModel = object : RecoveryViewModel(TODO("AuthApiService"), TODO("Context"), TODO("Json")) {
        override val uiState = MutableStateFlow(RecoveryState.Idle)
        override fun initiateRecovery() {}
        override fun createRegistrationChallenge() {}
        override fun registerPasskeyWithDevice(activityContext: Context, challengeData: ServerChallengeData) { /* Dummy impl */ }
    }
    RecoveryPasskeyRegistrationScreen(viewModel = dummyViewModel, onBackClick = {})
} 