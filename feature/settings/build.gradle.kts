/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
    alias(libs.plugins.peepsapp.android.feature)
    alias(libs.plugins.peepsapp.android.library.compose)
    alias(libs.plugins.peepsapp.android.library.jacoco)
}

android {
    namespace = "ai.peepsapp.peopleapp.feature.settings"
}

dependencies {
    implementation(libs.androidx.appcompat)
    implementation(libs.google.oss.licenses)
    implementation(projects.core.data) // Re-enabled for SettingsViewModel's UserDataRepository dependency

    implementation(projects.core.common) // For Base64UrlUtil etc.
    implementation(projects.core.network) // For AuthApiService and network models

    // Hilt for Dependency Injection
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    // ViewModel and LiveData (Lifecycle KTX)
    // implementation(libs.androidx.lifecycle.viewmodel.ktx) // Previous attempt
    implementation(libs.androidx.lifecycle.viewModelCompose) // Corrected alias based on toml

    // Coroutines
    implementation(libs.kotlinx.coroutines.android) // kotlinx.coroutines.core is usually a transitive dependency

    // FIDO / Credentials API
    implementation(libs.androidx.credentials.credentials) // Corrected alias based on toml (assuming -credentials for the base)
    // implementation(libs.play.services.fido) // Previous attempt
    implementation(libs.google.android.gms.play.services.fido) // Corrected alias based on toml

    // Kotlinx Serialization for JSON
    implementation(libs.kotlinx.serialization.json)

    testImplementation(projects.core.testing)

    androidTestImplementation(libs.bundles.androidx.compose.ui.test)
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions {
        allWarningsAsErrors = true
    }
}
