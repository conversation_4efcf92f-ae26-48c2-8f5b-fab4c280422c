<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright 2023 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="64dp"
    android:height="64dp"
    android:viewportWidth="64"
    android:viewportHeight="64">
  <path
      android:pathData="M16,2H55C57.209,2 59,3.791 59,6V52"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="8"
          android:startY="8"
          android:endX="56"
          android:endY="56"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFA8FF"/>
        <item android:offset="1" android:color="#FFFF8B5E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M45,10H9C6.791,10 5,11.791 5,14V55.854C5,59.177 8.817,61.051 11.446,59.019L24.554,48.89C25.995,47.777 28.005,47.777 29.446,48.89L42.554,59.019C45.183,61.051 49,59.177 49,55.854V14C49,11.791 47.209,10 45,10Z"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="8"
          android:startY="8"
          android:endX="56"
          android:endY="56"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFA8FF"/>
        <item android:offset="1" android:color="#FFFF8B5E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
