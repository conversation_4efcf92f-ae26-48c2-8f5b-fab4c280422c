// core/data/src/main/kotlin/ai/peepsapp/peopleapp/core/data/repository/OfflineFirstPersonRepository.kt
package ai.peepsapp.peopleapp.core.data.repository

import ai.peepsapp.peopleapp.core.database.dao.PersonDao
import ai.peepsapp.peopleapp.core.database.model.asExternalModel
import ai.peepsapp.peopleapp.core.model.data.Person
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * Offline-first implementation of the [PersonRepository].
 * Reads are primarily served from the local database.
 */
class OfflineFirstPersonRepository @Inject constructor(
    private val personDao: PersonDao
) : PersonRepository {

    override fun getPeople(): Flow<List<Person>> =
        personDao.getPeople().map { entities ->
            entities.map { it.asExternalModel() }
        }

    override fun getPerson(id: String): Flow<Person?> =
        personDao.getPerson(id).map { entity ->
            entity?.asExternalModel()
        }
} 