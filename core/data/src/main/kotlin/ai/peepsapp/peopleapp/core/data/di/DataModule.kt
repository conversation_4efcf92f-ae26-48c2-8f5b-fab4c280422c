/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ai.peepsapp.peopleapp.core.data.di

import ai.peepsapp.peopleapp.core.data.repository.DefaultRecentSearchRepository
// import ai.peepsapp.peopleapp.core.data.repository.DefaultSearchContentsRepository // Removed
import ai.peepsapp.peopleapp.core.data.repository.OfflineFirstUserDataRepository
import ai.peepsapp.peopleapp.core.data.repository.RecentSearchRepository
// import ai.peepsapp.peopleapp.core.data.repository.SearchContentsRepository // Removed
import ai.peepsapp.peopleapp.core.data.repository.UserDataRepository
import ai.peepsapp.peopleapp.core.data.repository.PersonRepository
import ai.peepsapp.peopleapp.core.data.repository.OfflineFirstPersonRepository
import ai.peepsapp.peopleapp.core.data.util.ConnectivityManagerNetworkMonitor
import ai.peepsapp.peopleapp.core.data.util.NetworkMonitor
import ai.peepsapp.peopleapp.core.data.util.TimeZoneBroadcastMonitor
import ai.peepsapp.peopleapp.core.data.util.TimeZoneMonitor
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {

    @Binds
    internal abstract fun bindsUserDataRepository(
        userDataRepository: OfflineFirstUserDataRepository,
    ): UserDataRepository

    @Binds
    internal abstract fun bindsRecentSearchRepository(
        recentSearchRepository: DefaultRecentSearchRepository,
    ): RecentSearchRepository

    @Binds
    internal abstract fun bindsNetworkMonitor(
        networkMonitor: ConnectivityManagerNetworkMonitor,
    ): NetworkMonitor

    @Binds
    internal abstract fun binds(impl: TimeZoneBroadcastMonitor): TimeZoneMonitor

    @Binds
    internal abstract fun bindsPersonRepository(
        personRepository: OfflineFirstPersonRepository,
    ): PersonRepository
}
