// core/data/src/main/kotlin/ai/peepsapp/peopleapp/core/data/repository/PersonRepository.kt
package ai.peepsapp.peopleapp.core.data.repository

import ai.peepsapp.peopleapp.core.model.data.Person
import kotlinx.coroutines.flow.Flow

/**
 * Data layer interface for the people
 */
interface PersonRepository {

    /**
     * Gets the stream of all persons, ordered by name.
     */
    fun getPeople(): Flow<List<Person>>

    /**
     * Gets the stream of a single person by ID.
     */
    fun getPerson(id: String): Flow<Person?>

    // TODO: Add methods for creating/updating/deleting persons as needed.
} 