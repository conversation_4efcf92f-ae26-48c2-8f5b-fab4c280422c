{"formatVersion": 1, "database": {"version": 2, "identityHash": "910a1df0aaff6e8e716c1e48dd4f9da7", "entities": [{"tableName": "news_resources", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL, `url` TEXT NOT NULL, `header_image_url` TEXT, `publish_date` INTEGER NOT NULL, `type` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "headerImageUrl", "columnName": "header_image_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "publishDate", "columnName": "publish_date", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "news_resources_topics", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`news_resource_id` TEXT NOT NULL, `topic_id` TEXT NOT NULL, PRIMARY KEY(`news_resource_id`, `topic_id`), FOREI<PERSON><PERSON> KEY(`news_resource_id`) REFERENCES `news_resources`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREI<PERSON><PERSON> KEY(`topic_id`) REFERENCES `topics`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "newsResourceId", "columnName": "news_resource_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "topicId", "columnName": "topic_id", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["news_resource_id", "topic_id"]}, "indices": [{"name": "index_news_resources_topics_news_resource_id", "unique": false, "columnNames": ["news_resource_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_news_resources_topics_news_resource_id` ON `${TABLE_NAME}` (`news_resource_id`)"}, {"name": "index_news_resources_topics_topic_id", "unique": false, "columnNames": ["topic_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_news_resources_topics_topic_id` ON `${TABLE_NAME}` (`topic_id`)"}], "foreignKeys": [{"table": "news_resources", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["news_resource_id"], "referencedColumns": ["id"]}, {"table": "topics", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["topic_id"], "referencedColumns": ["id"]}]}, {"ftsVersion": "FTS4", "ftsOptions": {"tokenizer": "simple", "tokenizerArgs": [], "contentTable": "", "languageIdColumnName": "", "matchInfo": "FTS4", "notIndexedColumns": [], "prefixSizes": [], "preferredOrder": "ASC"}, "contentSyncTriggers": [], "tableName": "newsResourcesFts", "createSql": "CREATE VIRTUAL TABLE IF NOT EXISTS `${TABLE_NAME}` USING FTS4(`newsResourceId` TEXT NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL)", "fields": [{"fieldPath": "newsResourceId", "columnName": "newsResourceId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": []}, "indices": [], "foreignKeys": []}, {"tableName": "topics", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `shortDescription` TEXT NOT NULL, `longDescription` TEXT NOT NULL DEFAULT '', `url` TEXT NOT NULL DEFAULT '', `imageUrl` TEXT NOT NULL DEFAULT '', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "shortDescription", "columnName": "shortDescription", "affinity": "TEXT", "notNull": true}, {"fieldPath": "longDescription", "columnName": "longDescription", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"ftsVersion": "FTS4", "ftsOptions": {"tokenizer": "simple", "tokenizerArgs": [], "contentTable": "", "languageIdColumnName": "", "matchInfo": "FTS4", "notIndexedColumns": [], "prefixSizes": [], "preferredOrder": "ASC"}, "contentSyncTriggers": [], "tableName": "topicsFts", "createSql": "CREATE VIRTUAL TABLE IF NOT EXISTS `${TABLE_NAME}` USING FTS4(`topicId` TEXT NOT NULL, `name` TEXT NOT NULL, `shortDescription` TEXT NOT NULL, `longDescription` TEXT NOT NULL)", "fields": [{"fieldPath": "topicId", "columnName": "topicId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "shortDescription", "columnName": "shortDescription", "affinity": "TEXT", "notNull": true}, {"fieldPath": "longDescription", "columnName": "longDescription", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": []}, "indices": [], "foreignKeys": []}, {"tableName": "recentSearchQueries", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`query` TEXT NOT NULL, `queriedDate` INTEGER NOT NULL, PRIMARY KEY(`query`))", "fields": [{"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}, {"fieldPath": "queriedDate", "columnName": "queriedDate", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["query"]}, "indices": [], "foreignKeys": []}, {"tableName": "persons", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `photo_uri` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "photoUri", "columnName": "photo_uri", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '910a1df0aaff6e8e716c1e48dd4f9da7')"]}}