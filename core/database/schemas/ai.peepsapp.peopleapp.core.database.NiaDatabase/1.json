{"formatVersion": 1, "database": {"version": 1, "identityHash": "6cbde20277864edc129828d888343cb8", "entities": [{"tableName": "recentSearchQueries", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`query` TEXT NOT NULL, `queriedDate` INTEGER NOT NULL, PRIMARY KEY(`query`))", "fields": [{"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}, {"fieldPath": "queriedDate", "columnName": "queriedDate", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["query"]}, "indices": [], "foreignKeys": []}, {"tableName": "persons", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `photo_uri` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "photoUri", "columnName": "photo_uri", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '6cbde20277864edc129828d888343cb8')"]}}