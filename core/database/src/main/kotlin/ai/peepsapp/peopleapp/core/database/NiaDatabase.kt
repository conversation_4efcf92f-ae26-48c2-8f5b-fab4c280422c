/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ai.peepsapp.peopleapp.core.database

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import ai.peepsapp.peopleapp.core.database.dao.RecentSearchQueryDao
import ai.peepsapp.peopleapp.core.database.dao.PersonDao
import ai.peepsapp.peopleapp.core.database.model.RecentSearchQueryEntity
import ai.peepsapp.peopleapp.core.database.model.PersonEntity
import ai.peepsapp.peopleapp.core.database.util.InstantConverter

@Database(
    entities = [
        RecentSearchQueryEntity::class,
        PersonEntity::class,
    ],
    version = 1,
    exportSchema = true,
)
@TypeConverters(
    InstantConverter::class,
)
internal abstract class NiaDatabase : RoomDatabase() {
    abstract fun recentSearchQueryDao(): RecentSearchQueryDao
    abstract fun personDao(): PersonDao
}
