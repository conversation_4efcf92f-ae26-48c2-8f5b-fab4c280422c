// core/database/src/main/kotlin/ai/peepsapp/peopleapp/core/database/dao/PersonDao.kt
package ai.peepsapp.peopleapp.core.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import ai.peepsapp.peopleapp.core.database.model.PersonEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for [PersonEntity] access.
 */
@Dao
interface PersonDao {
    /**
     * Observes the list of all persons.
     *
     * @return A Flow emitting the list of persons.
     */
    @Query("SELECT * FROM persons ORDER BY name ASC") // Order alphabetically
    fun getPeople(): Flow<List<PersonEntity>>

    /**
     * Observes a single person by their ID.
     *
     * @param personId The ID of the person to observe.
     * @return A Flow emitting the person, or null if not found.
     */
    @Query("SELECT * FROM persons WHERE id = :personId")
    fun getPerson(personId: String): Flow<PersonEntity?> // Return nullable in case ID doesn't exist

    /**
     * Inserts or replaces persons in the database.
     *
     * @param persons The list of persons to insert or replace.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplacePeople(persons: List<PersonEntity>)

    // TODO: Consider adding delete methods if needed later.
} 