// core/database/src/main/kotlin/ai/peepsapp/peopleapp/core/database/model/PersonEntity.kt
package ai.peepsapp.peopleapp.core.database.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import ai.peepsapp.peopleapp.core.model.data.Person

/**
 * Defines a person entity for the Room database.
 */
@Entity(
    tableName = "persons", // Plural table name is conventional
)
data class PersonEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    @ColumnInfo(name = "photo_uri") // Use snake_case for column names
    val photoUri: String?,
)

/**
 * Converts a [PersonEntity] to the core [Person] model.
 */
fun PersonEntity.asExternalModel() = Person(
    id = id,
    name = name,
    photoUri = photoUri,
) 