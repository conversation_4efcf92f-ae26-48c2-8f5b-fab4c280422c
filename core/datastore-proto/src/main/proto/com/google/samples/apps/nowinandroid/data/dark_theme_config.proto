/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";

option java_package = "ai.peepsapp.peopleapp.core.datastore";
option java_multiple_files = true;

enum DarkThemeConfigProto {
  DARK_THEME_CONFIG_UNSPECIFIED = 0;
  DARK_THEME_CONFIG_FOLLOW_SYSTEM = 1;
  DARK_THEME_CONFIG_LIGHT = 2;
  DARK_THEME_CONFIG_DARK = 3;
}
