package ai.peepsapp.peopleapp.core.datastore

import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import ai.peepsapp.peopleapp.core.model.data.DarkThemeConfig
import ai.peepsapp.peopleapp.core.model.data.ThemeBrand
import ai.peepsapp.peopleapp.core.model.data.UserData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject

class PeepsPreferencesDataSource @Inject constructor(
    private val userPreferences: DataStore<Preferences>,
) {
    object UserPreferencesKeys {
        val FOLLOWED_TOPIC_IDS = stringSetPreferencesKey("followed_topic_ids")
        val VIEWED_NEWS_RESOURCE_IDS = stringSetPreferencesKey("viewed_news_resource_ids")
        val BOOKMARKED_NEWS_RESOURCE_IDS = stringSetPreferencesKey("bookmarked_news_resource_ids")
        val THEME_BRAND = stringPreferencesKey("theme_brand")
        val DARK_THEME_CONFIG = stringPreferencesKey("dark_theme_config")
        val USE_DYNAMIC_COLOR = booleanPreferencesKey("use_dynamic_color")
        val SHOULD_HIDE_ONBOARDING = booleanPreferencesKey("should_hide_onboarding")
        val USER_ID = stringPreferencesKey("user_id")
        val SESSION_TOKEN = stringPreferencesKey("session_token")
    }

    val userData: Flow<UserData> = userPreferences.data
        .catch { exception ->
            if (exception is IOException) {
                Log.e("PeepsPreferences", "Error reading preferences.", exception)
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map {
            UserData(
                bookmarkedNewsResources = it[UserPreferencesKeys.BOOKMARKED_NEWS_RESOURCE_IDS] ?: emptySet(),
                viewedNewsResources = it[UserPreferencesKeys.VIEWED_NEWS_RESOURCE_IDS] ?: emptySet(),
                followedTopics = it[UserPreferencesKeys.FOLLOWED_TOPIC_IDS] ?: emptySet(),
                themeBrand = it[UserPreferencesKeys.THEME_BRAND]?.let { ThemeBrand.valueOf(it) } ?: ThemeBrand.DEFAULT,
                darkThemeConfig = it[UserPreferencesKeys.DARK_THEME_CONFIG]?.let { DarkThemeConfig.valueOf(it) } ?: DarkThemeConfig.FOLLOW_SYSTEM,
                useDynamicColor = it[UserPreferencesKeys.USE_DYNAMIC_COLOR] ?: false,
                shouldHideOnboarding = it[UserPreferencesKeys.SHOULD_HIDE_ONBOARDING] ?: false,
                userId = it[UserPreferencesKeys.USER_ID],
                sessionToken = it[UserPreferencesKeys.SESSION_TOKEN]
            )
        }

    suspend fun setFollowedTopicIds(topicIds: Set<String>) {
        try {
            userPreferences.edit {
                it[UserPreferencesKeys.FOLLOWED_TOPIC_IDS] = topicIds
            }
        } catch (ioException: IOException) {
            Log.e("PeepsPreferences", "Failed to update followed topic IDs.", ioException)
        }
    }

    suspend fun setTopicIdFollowed(topicId: String, followed: Boolean) {
        try {
            userPreferences.edit {
                val currentFollowedTopics = it[UserPreferencesKeys.FOLLOWED_TOPIC_IDS] ?: emptySet()
                if (followed) {
                    it[UserPreferencesKeys.FOLLOWED_TOPIC_IDS] = currentFollowedTopics + topicId
                } else {
                    it[UserPreferencesKeys.FOLLOWED_TOPIC_IDS] = currentFollowedTopics - topicId
                }
            }
        } catch (ioException: IOException) {
            Log.e("PeepsPreferences", "Failed to update topic ID followed status.", ioException)
        }
    }

    suspend fun setThemeBrand(themeBrand: ThemeBrand) {
        userPreferences.edit {
            it[UserPreferencesKeys.THEME_BRAND] = themeBrand.name
        }
    }

    suspend fun setDynamicColorPreference(useDynamicColor: Boolean) {
        userPreferences.edit {
            it[UserPreferencesKeys.USE_DYNAMIC_COLOR] = useDynamicColor
        }
    }

    suspend fun setDarkThemeConfig(darkThemeConfig: DarkThemeConfig) {
        userPreferences.edit {
            it[UserPreferencesKeys.DARK_THEME_CONFIG] = darkThemeConfig.name
        }
    }

    suspend fun setNewsResourceBookmarked(newsResourceId: String, bookmarked: Boolean) {
        try {
            userPreferences.edit {
                val currentBookmarked = it[UserPreferencesKeys.BOOKMARKED_NEWS_RESOURCE_IDS] ?: emptySet()
                if (bookmarked) {
                    it[UserPreferencesKeys.BOOKMARKED_NEWS_RESOURCE_IDS] = currentBookmarked + newsResourceId
                } else {
                    it[UserPreferencesKeys.BOOKMARKED_NEWS_RESOURCE_IDS] = currentBookmarked - newsResourceId
                }
            }
        } catch (ioException: IOException) {
            Log.e("PeepsPreferences", "Failed to update news resource bookmark status.", ioException)
        }
    }

    suspend fun setNewsResourceViewed(newsResourceId: String, viewed: Boolean) {
        setNewsResourcesViewed(listOf(newsResourceId), viewed)
    }

    suspend fun setNewsResourcesViewed(newsResourceIds: List<String>, viewed: Boolean) {
         userPreferences.edit { prefs ->
            val currentViewed = prefs[UserPreferencesKeys.VIEWED_NEWS_RESOURCE_IDS] ?: emptySet()
            if (viewed) {
                prefs[UserPreferencesKeys.VIEWED_NEWS_RESOURCE_IDS] = currentViewed + newsResourceIds.toSet()
            } else {
                prefs[UserPreferencesKeys.VIEWED_NEWS_RESOURCE_IDS] = currentViewed - newsResourceIds.toSet()
            }
        }
    }

    suspend fun setShouldHideOnboarding(shouldHideOnboarding: Boolean) {
        userPreferences.edit {
            it[UserPreferencesKeys.SHOULD_HIDE_ONBOARDING] = shouldHideOnboarding
        }
    }

    suspend fun updateSessionData(userId: String, sessionToken: String) {
        userPreferences.edit {
            it[UserPreferencesKeys.USER_ID] = userId
            it[UserPreferencesKeys.SESSION_TOKEN] = sessionToken
        }
    }

    suspend fun clearSessionData() {
        userPreferences.edit {
            it.remove(UserPreferencesKeys.USER_ID)
            it.remove(UserPreferencesKeys.SESSION_TOKEN)
        }
    }

    suspend fun clearViewedNewsResources() {
        userPreferences.edit {
            it.remove(UserPreferencesKeys.VIEWED_NEWS_RESOURCE_IDS)
        }
    }
} 