/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ai.peepsapp.peopleapp.core.datastore.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import ai.peepsapp.peopleapp.core.network.Dispatcher
import ai.peepsapp.peopleapp.core.network.NiaDispatchers.IO
import ai.peepsapp.peopleapp.core.network.di.ApplicationScope
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

// Define the DataStore name for Preferences DataStore
private const val USER_PREFERENCES_NAME = "peeps_user_preferences"

// Extension property to create the DataStore instance
private val Context.peepsUserPreferencesStore: DataStore<Preferences> by preferencesDataStore(
    name = USER_PREFERENCES_NAME
)

@Module
@InstallIn(SingletonComponent::class)
object DataStoreModule {

    @Provides
    @Singleton
    internal fun providesUserPreferencesDataStore(
        @ApplicationContext context: Context,
        // CoroutineDispatcher and CoroutineScope might not be strictly needed for simple Preferences DataStore provision
        // but can be kept if other parts of the module use them, or for future expansion.
        // @Dispatcher(IO) ioDispatcher: CoroutineDispatcher,
        // @ApplicationScope scope: CoroutineScope,
    ): DataStore<Preferences> = // Return type changed to DataStore<Preferences>
        context.peepsUserPreferencesStore // Use the extension property
}
