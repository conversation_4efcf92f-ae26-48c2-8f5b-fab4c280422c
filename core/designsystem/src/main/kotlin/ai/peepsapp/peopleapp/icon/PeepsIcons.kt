/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ai.peepsapp.peopleapp.core.designsystem.icon

import androidx.annotation.DrawableRes
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBack
import androidx.compose.material.icons.automirrored.rounded.ShortText
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.outlined.Bookmarks
import androidx.compose.material.icons.outlined.Upcoming
import androidx.compose.material.icons.rounded.Add
import androidx.compose.material.icons.rounded.Bookmark
import androidx.compose.material.icons.rounded.BookmarkBorder
import androidx.compose.material.icons.rounded.Bookmarks
import androidx.compose.material.icons.rounded.Check
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.Grid3x3
import androidx.compose.material.icons.rounded.Person
import androidx.compose.material.icons.rounded.Search
import androidx.compose.material.icons.rounded.Settings
import androidx.compose.material.icons.rounded.Upcoming
import androidx.compose.material.icons.rounded.ViewDay
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import ai.peepsapp.peopleapp.core.designsystem.R // Import R from core.designsystem

/**
 * Peeps App icons. Material icons are [ImageVector]s, custom icons are drawable resource IDs.
 */
object PeepsIcons {
    // Material Icons
    val Add: ImageVector = Icons.Rounded.Add
    val ArrowBack: ImageVector = Icons.AutoMirrored.Rounded.ArrowBack
    val Bookmark: ImageVector = Icons.Rounded.Bookmark
    val BookmarkBorder: ImageVector = Icons.Rounded.BookmarkBorder
    val Bookmarks: ImageVector = Icons.Rounded.Bookmarks
    val BookmarksBorder: ImageVector = Icons.Outlined.Bookmarks
    val Check: ImageVector = Icons.Rounded.Check
    val Close: ImageVector = Icons.Rounded.Close
    val Grid3x3: ImageVector = Icons.Rounded.Grid3x3
    val MoreVert: ImageVector = Icons.Default.MoreVert
    val Person: ImageVector = Icons.Rounded.Person
    val Search: ImageVector = Icons.Rounded.Search
    val ShortText: ImageVector = Icons.AutoMirrored.Rounded.ShortText
    val Upcoming: ImageVector = Icons.Rounded.Upcoming
    val UpcomingBorder: ImageVector = Icons.Outlined.Upcoming
    val ViewDay: ImageVector = Icons.Rounded.ViewDay

    // Custom Icons from Drawables (IDs)
    @DrawableRes val AccountCircle: Int = R.drawable.ic_account_circle_24dp
    @DrawableRes val Chat: Int = R.drawable.ic_chat_black_24dp
    @DrawableRes val Community: Int = R.drawable.ic_community_black_24dp
    @DrawableRes val Dashboard: Int = R.drawable.ic_dashboard_black_24dp
    @DrawableRes val Forum: Int = R.drawable.forum_24
    @DrawableRes val Help: Int = R.drawable.ic_help_24dp
    @DrawableRes val Home: Int = R.drawable.ic_home_black_24dp
    @DrawableRes val Info: Int = R.drawable.ic_info_24dp
    @DrawableRes val Notifications: Int = R.drawable.ic_notifications_black_24dp // Using black variant
    @DrawableRes val People: Int = R.drawable.ic_people_black_24dp
    @DrawableRes val Settings: Int = R.drawable.ic_settings_black_24dp
    @DrawableRes val Sparkle: Int = R.drawable.ic_sparkle_button_24dp

    // Placeholder icon ID if needed
    @DrawableRes val Placeholder: Int = R.drawable.core_designsystem_ic_placeholder_default
}
