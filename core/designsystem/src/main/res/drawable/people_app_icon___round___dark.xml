<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="1024dp" android:viewportHeight="1024" android:viewportWidth="1024" android:width="1024dp">
      
    <path android:fillColor="#000000" android:pathData="M512,0L512,0A512,512 0,0 1,1024 512L1024,512A512,512 0,0 1,512 1024L512,1024A512,512 0,0 1,0 512L0,512A512,512 0,0 1,512 0z"/>
      
    <path android:fillType="evenOdd" android:pathData="M698.3,827.1C792.9,827.1 869.7,750.4 869.7,655.8C869.7,597.6 840.7,546.2 796.3,515.2C770.9,561 689.4,530.1 698.3,484.5C603.7,484.5 527,561.2 527,655.8C527,750.4 603.7,827.1 698.3,827.1Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="741.8" android:endY="754.7" android:startX="623.3" android:startY="556.9" android:type="linear">
                        
                <item android:color="#FF5FBEFC" android:offset="0"/>
                        
                <item android:color="#FF208CFC" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M698.3,484.5C734.8,484.5 768.6,495.8 796.3,515.2C770.9,561 689.4,530.1 698.3,484.5Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="735" android:endY="655.8" android:startX="704.3" android:startY="471.7" android:type="linear">
                        
                <item android:color="#FF5FBEFC" android:offset="0"/>
                        
                <item android:color="#FF208CFC" android:offset="0.6"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M701.8,488.5C734.7,489.2 765.3,499.3 791,516.4C778.9,534.2 756.1,537.3 735.8,530.3C715.3,523.3 700.1,507.1 701.8,488.5ZM531,655.8C531,564.9 603.4,490.9 693.7,488.5C692,511.9 711.3,530.4 733.2,537.9C755.4,545.5 782.8,542.8 797.5,521C838.8,551.5 865.7,600.5 865.7,655.8C865.7,748.2 790.7,823.1 698.3,823.1C605.9,823.1 531,748.2 531,655.8Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="698.3" android:endY="827.1" android:startX="698.3" android:startY="484.5" android:type="linear">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M783.1,396.2m-65.7,0a65.7,65.7 0,1 1,131.3 0a65.7,65.7 0,1 1,-131.3 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="764.4" android:centerY="371.8" android:gradientRadius="98.5" android:type="radial">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M783.1,396.2m-61.7,0a61.7,61.7 0,1 1,123.3 0a61.7,61.7 0,1 1,-123.3 0" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="783.1" android:endY="461.8" android:startX="783.1" android:startY="330.5" android:type="linear">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M534.4,252.7m-65.7,0a65.7,65.7 0,1 1,131.3 0a65.7,65.7 0,1 1,-131.3 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="511" android:centerY="237.9" android:gradientRadius="88.6" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M534.4,252.7m-64.2,0a64.2,64.2 0,1 1,128.3 0a64.2,64.2 0,1 1,-128.3 0" android:strokeWidth="3">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="511" android:centerY="237.9" android:gradientRadius="88.6" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M534.4,252.7m-61.7,0a61.7,61.7 0,1 1,123.3 0a61.7,61.7 0,1 1,-123.3 0" android:strokeWidth="8">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="483.9" android:centerY="213.1" android:gradientRadius="151.9" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D846C760" android:offset="0.1"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="511" android:centerY="237.9" android:gradientRadius="88.6" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <group>
            
        <clip-path android:pathData="M534.4,252.7m-65.7,0a65.7,65.7 0,1 1,131.3 0a65.7,65.7 0,1 1,-131.3 0"/>
            
        <path android:pathData="M509.2,225.7l117.9,81.7l-79.5,79.1l-117.9,-81.7z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="516.7" android:endY="263.7" android:startX="509.2" android:startY="225.7" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M509.2,225.7l117.9,81.7l79.5,-79.1l-117.9,-81.7z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="555.5" android:endY="225.1" android:startX="509.2" android:startY="225.7" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M509.2,225.7l-117.9,-81.7l-79.5,79.1l117.9,81.7z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="462.8" android:endY="226.3" android:startX="509.2" android:startY="225.7" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M509.2,225.7l-117.9,-81.7l79.5,-79.1l117.9,81.7z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="501.6" android:endY="187.7" android:startX="509.2" android:startY="225.7" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
          
    </group>
      
    <path android:pathData="M320.4,376.2m-65.7,0a65.7,65.7 0,1 1,131.3 0a65.7,65.7 0,1 1,-131.3 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="292" android:centerY="349.9" android:gradientRadius="97.7" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M320.4,376.2m-61.7,0a61.7,61.7 0,1 1,123.3 0a61.7,61.7 0,1 1,-123.3 0" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="292" android:centerY="349.9" android:gradientRadius="97.7" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillType="evenOdd" android:pathData="M273.9,477.5H218C204.2,477.5 193,488.7 193,502.5V802.1C193,815.9 204.2,827.1 218,827.1H422.8C436.6,827.1 447.8,815.9 447.8,802.1V502.5C447.8,488.7 436.6,477.5 422.8,477.5H365.6L341.2,519C331.5,535.5 307.7,535.4 298.1,518.9L273.9,477.5Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="265.2" android:centerY="582.3" android:gradientRadius="239" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M273.9,477.5L298.1,518.9C307.7,535.4 331.5,535.5 341.2,519L365.6,477.5H273.9Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="294.3" android:centerY="397.5" android:gradientRadius="153.1" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M218,481.5H271.6L294.6,520.9C305.8,540.1 333.4,540.1 344.6,521L367.9,481.5H422.8C434.4,481.5 443.8,490.9 443.8,502.5V802.1C443.8,813.7 434.4,823.1 422.8,823.1H218C206.4,823.1 197,813.7 197,802.1V502.5C197,490.9 206.4,481.5 218,481.5ZM358.6,481.5L337.7,517C329.6,530.8 309.6,530.8 301.5,516.9L280.9,481.5H358.6Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="265.2" android:centerY="582.3" android:gradientRadius="239" android:type="radial">
                        
                <item android:color="#FFFCC625" android:offset="0"/>
                        
                <item android:color="#FFFEE658" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M367.8,828H695.8C713.4,828 725.5,810.4 719.2,794L555.7,369.2C547.5,347.9 517.3,347.8 509.1,369.1L344.5,794C338.1,810.4 350.2,828 367.8,828Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="460.4" android:centerY="510.2" android:gradientRadius="329.4" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M695.8,824H367.8C353,824 342.9,809.2 348.2,795.4L512.8,370.6C519.7,352.7 545.1,352.7 552,370.6L715.4,795.5C720.7,809.2 710.6,824 695.8,824Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="460.4" android:centerY="510.2" android:gradientRadius="329.4" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <group>
            
        <clip-path android:pathData="M367.8,828H695.8C713.4,828 725.5,810.4 719.2,794L555.7,369.2C547.5,347.9 517.3,347.8 509.1,369.1L344.5,794C338.1,810.4 350.2,828 367.8,828Z"/>
            
        <path android:pathData="M481.3,513.6l334.8,136.9l-135.6,331.6l-334.8,-136.9z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="527.9" android:endY="624.5" android:startX="481.3" android:startY="513.6" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M481.3,513.6l334.8,136.9l135.6,-331.6l-334.8,-136.9z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="592.2" android:endY="467.1" android:startX="481.3" android:startY="513.6" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M481.3,513.6l-334.8,-136.9l-135.6,331.6l334.8,136.9z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="370.4" android:endY="560.1" android:startX="481.3" android:startY="513.6" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M481.3,513.6l-334.8,-136.9l135.6,-331.6l334.8,136.9z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="434.8" android:endY="402.7" android:startX="481.3" android:startY="513.6" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
          
    </group>
    
</vector>
