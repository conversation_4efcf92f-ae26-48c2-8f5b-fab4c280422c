<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="1024dp" android:viewportHeight="1024" android:viewportWidth="1024" android:width="1024dp">
      
    <path android:fillColor="#ffffff" android:pathData="M0,0h1024v1024h-1024z"/>
      
    <path android:fillType="evenOdd" android:pathData="M736,861C844.8,861 933,772.8 933,664C933,597.1 899.7,538 848.7,502.4C819.5,555 725.7,519.4 736,467C627.2,467 539,555.2 539,664C539,772.8 627.2,861 736,861Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="786" android:endY="777.7" android:startX="649.8" android:startY="550.3" android:type="linear">
                        
                <item android:color="#FF5FBEFC" android:offset="0"/>
                        
                <item android:color="#FF208CFC" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M736,467C777.9,467 816.8,480.1 848.7,502.4C819.5,555 725.7,519.4 736,467Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="778.1" android:endY="664" android:startX="742.9" android:startY="452.3" android:type="linear">
                        
                <item android:color="#FF5FBEFC" android:offset="0"/>
                        
                <item android:color="#FF208CFC" android:offset="0.6"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M776.2,527.9C801.8,536.7 833.2,533.4 849.9,508.2C897.9,543.3 929,600 929,664C929,770.6 842.6,857 736,857C629.4,857 543,770.6 543,664C543,559 626.9,473.5 731.4,471.1C729,497.9 751.1,519.2 776.2,527.9ZM843.3,503.6C829.4,524.8 802.5,528.4 778.8,520.3C755,512.1 737.1,493 739.4,471C777.9,471.7 813.5,483.6 843.3,503.6Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="736" android:endY="861" android:startX="736" android:startY="467" android:type="linear">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M833.5,365.5m-75.5,0a75.5,75.5 0,1 1,151 0a75.5,75.5 0,1 1,-151 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="812" android:centerY="337.5" android:gradientRadius="113.3" android:type="radial">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M833.5,365.5m-71.5,0a71.5,71.5 0,1 1,143 0a71.5,71.5 0,1 1,-143 0" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="833.5" android:endY="441" android:startX="833.5" android:startY="290" android:type="linear">
                        
                <item android:color="#FF5EBEFC" android:offset="0"/>
                        
                <item android:color="#FF1D87FB" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M547.5,200.5m-75.5,0a75.5,75.5 0,1 1,151 0a75.5,75.5 0,1 1,-151 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="520.6" android:centerY="183.6" android:gradientRadius="101.9" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M547.5,200.5m-74,0a74,74 0,1 1,148 0a74,74 0,1 1,-148 0" android:strokeWidth="3">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="520.6" android:centerY="183.6" android:gradientRadius="101.9" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M547.5,200.5m-71.5,0a71.5,71.5 0,1 1,143 0a71.5,71.5 0,1 1,-143 0" android:strokeWidth="8">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="489.5" android:centerY="155" android:gradientRadius="174.6" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D846C760" android:offset="0.1"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="520.6" android:centerY="183.6" android:gradientRadius="101.9" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <group>
            
        <clip-path android:pathData="M547.5,200.5m-75.5,0a75.5,75.5 0,1 1,151 0a75.5,75.5 0,1 1,-151 0"/>
            
        <path android:pathData="M518.5,169.5l134.1,93l-90.2,89.8l-134.1,-93z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="527.2" android:endY="213.2" android:startX="518.5" android:startY="169.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M518.5,169.5l134.1,93l90.2,-89.8l-134.1,-93z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="571.8" android:endY="168.8" android:startX="518.5" android:startY="169.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M518.5,169.5l-134.1,-93l-90.2,89.8l134.1,93z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="465.2" android:endY="170.2" android:startX="518.5" android:startY="169.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M518.5,169.5l-134.1,-93l90.2,-89.8l134.1,93z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="509.8" android:endY="125.8" android:startX="518.5" android:startY="169.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
          
    </group>
      
    <path android:pathData="M301.5,342.5m-75.5,0a75.5,75.5 0,1 1,151 0a75.5,75.5 0,1 1,-151 0">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="268.8" android:centerY="312.3" android:gradientRadius="112.3" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M301.5,342.5m-71.5,0a71.5,71.5 0,1 1,143 0a71.5,71.5 0,1 1,-143 0" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="268.8" android:centerY="312.3" android:gradientRadius="112.3" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillType="evenOdd" android:pathData="M248,459H180C166.2,459 155,470.2 155,484V836C155,849.8 166.2,861 180,861H423C436.8,861 448,849.8 448,836V484C448,470.2 436.8,459 423,459H353.5L322.1,512.2C312.5,528.7 288.6,528.7 279,512.2L248,459Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="238" android:centerY="579.5" android:gradientRadius="274.8" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M248,459L279,512.2C288.6,528.7 312.5,528.7 322.1,512.2L353.5,459H248Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="271.5" android:centerY="367" android:gradientRadius="176" android:type="radial">
                        
                <item android:color="#FFFEE658" android:offset="0"/>
                        
                <item android:color="#FFFCC625" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M255,463H346.5L318.7,510.2C310.6,524 290.5,524 282.5,510.1L255,463ZM245.7,463L275.6,514.2C286.7,533.3 314.3,533.4 325.6,514.3L355.8,463H423C434.6,463 444,472.4 444,484V836C444,847.6 434.6,857 423,857H180C168.4,857 159,847.6 159,836V484C159,472.4 168.4,463 180,463H245.7Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="238" android:centerY="579.5" android:gradientRadius="274.8" android:type="radial">
                        
                <item android:color="#FFFCC625" android:offset="0"/>
                        
                <item android:color="#FFFEE658" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M350.5,862H738.6C756.1,862 768.2,844.4 761.9,828L568.5,325.4C560.3,304.1 530.2,304.1 521.9,325.4L327.2,828C320.8,844.4 332.9,862 350.5,862Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:centerX="462.5" android:centerY="496.6" android:gradientRadius="378.7" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M738.6,858H350.5C335.7,858 325.6,843.2 330.9,829.4L525.6,326.8C532.6,308.9 557.9,308.9 564.8,326.9L758.2,829.5C763.5,843.2 753.3,858 738.6,858Z" android:strokeWidth="8">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:centerX="462.5" android:centerY="496.6" android:gradientRadius="378.7" android:type="radial">
                        
                <item android:color="#D846C760" android:offset="0"/>
                        
                <item android:color="#D812AB52" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <group>
            
        <clip-path android:pathData="M350.5,862H738.6C756.1,862 768.2,844.4 761.9,828L568.5,325.4C560.3,304.1 530.2,304.1 521.9,325.4L327.2,828C320.8,844.4 332.9,862 350.5,862Z"/>
            
        <path android:pathData="M486.5,500.5l383.5,156.8l-155.3,379.8l-383.5,-156.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="540" android:endY="628" android:startX="486.5" android:startY="500.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M486.5,500.5l383.5,156.8l155.3,-379.8l-383.5,-156.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="614" android:endY="447" android:startX="486.5" android:startY="500.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M486.5,500.5l-383.5,-156.8l-155.3,379.8l383.5,156.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="359" android:endY="554" android:startX="486.5" android:startY="500.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M486.5,500.5l-383.5,-156.8l155.3,-379.8l383.5,156.8z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="433" android:endY="373" android:startX="486.5" android:startY="500.5" android:type="linear">
                              
                    <item android:color="#99D9FFE1" android:offset="0"/>
                              
                    <item android:color="#9985DCA5" android:offset="0.2"/>
                              
                    <item android:color="#0012AB52" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
          
    </group>
    
</vector>
