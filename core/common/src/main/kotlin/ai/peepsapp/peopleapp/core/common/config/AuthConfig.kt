package ai.peepsapp.peopleapp.core.common.config

/**
 * Configuration constants related to Authentication.
 */
object AuthConfig {
    // Relying Party ID for WebAuthn/Passkeys
    // const val RELYING_PARTY_ID = "local.peepsapp.ai"
    const val RELYING_PARTY_ID = "stage.peepsapp.ai"

    // Base URL for the backend authentication API
    // const val API_BASE_URL = "https://local.peepsapp.ai:8443"
    const val API_BASE_URL = "https://stage.peepsapp.ai"

    // Default timeout for WebAuthn operations (in milliseconds)
    const val AUTH_TIMEOUT_MS = 60000L
} 