// core/common/src/main/kotlin/ai/peepsapp/peopleapp/core/common/serialization/Base64UrlByteArraySerializer.kt
package ai.peepsapp.peopleapp.core.common.serialization

import ai.peepsapp.peopleapp.core.common.util.Base64UrlUtil
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.modules.contextual

object Base64UrlByteArraySerializer : KSerializer<ByteArray> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("ByteArrayAsBase64Url", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: ByteArray) {
        encoder.encodeString(Base64UrlUtil.encode(value))
    }

    override fun deserialize(decoder: Decoder): ByteArray {
        // This will be used if we ever need to deserialize a Base64URL string directly to ByteArray
        // For FidoPublicKeyCredentialCreationOptions, we only care about serialization.
        return Base64UrlUtil.decode(decoder.decodeString())
    }
}

// It can be helpful to provide a ready-made module if this serializer is often needed with ByteArrays
val base64UrlByteArraySerializersModule = SerializersModule {
    contextual(Base64UrlByteArraySerializer)
} 