<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <string name="core_ui_bookmark">Bookmark</string>
    <string name="core_ui_unbookmark">Unbookmark</string>
    <string name="core_ui_back">Back</string>

    <string name="core_ui_unread_resource_dot_content_description">Unread</string>

    <string name="core_ui_card_tap_action">Open Resource Link</string>
    <string name="core_ui_card_meta_data_text">%1$s • %2$s</string>

    <string name="core_ui_topic_chip_content_description_when_followed">%1$s is followed</string>
    <string name="core_ui_topic_chip_content_description_when_not_followed">%1$s is not followed</string>

    <string name="core_ui_interests_card_follow_button_content_desc">Follow interest</string>
    <string name="core_ui_interests_card_unfollow_button_content_desc">Unfollow interest</string>
    <string name="core_ui_feed_sharing">Feed sharing</string>
    <string name="core_ui_feed_sharing_data">%1$s: %2$s</string>
</resources>
