package ai.peepsapp.peopleapp.core.network.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// --- /auth/register/challenge ---

@Serializable
data class RegisterChallengeRequest(
    val email: String,
    @SerialName("invite_token") val inviteToken: String? = null,
    @SerialName("device_name") val deviceName: String? = null
)

@Serializable
data class RegisterChallengeResponse(
    val options: PublicKeyCredentialCreationOptionsContainer? = null,
    @SerialName("user_id") val userId: String? = null, // Included in the example, might be useful
    // Include other potential fields like success/message if the backend sends them
)

@Serializable
data class PublicKeyCredentialCreationOptionsContainer(
    @SerialName("publicKey") val publicKey: PublicKeyCredentialCreationOptions? = null
)

// Based on WebAuthn spec and the example response
@Serializable
data class PublicKeyCredentialCreationOptions(
    val rp: RelyingPartyDetails? = null,
    val user: UserDetails? = null,
    val challenge: String? = null, // Base64 encoded
    val pubKeyCredParams: List<PubKeyCredParam>? = null,
    val timeout: Long? = null,
    val excludeCredentials: List<CredentialDescriptor>? = null,
    val authenticatorSelection: AuthenticatorSelectionCriteria? = null,
    val attestation: String? = null // e.g., "none", "direct"
)

@Serializable
data class RelyingPartyDetails(
    val name: String? = null,
    val id: String? = null // RP ID
)

@Serializable
data class UserDetails(
    val name: String? = null,
    val id: String? = null, // Base64 encoded user handle
    val displayName: String? = null
)

@Serializable
data class PubKeyCredParam(
    val type: String? = null, // e.g., "public-key"
    val alg: Int? = null // COSE algorithm identifier (e.g., -7 for ES256, -257 for RS256)
)

@Serializable
data class CredentialDescriptor(
    val type: String? = null, // e.g., "public-key"
    val id: String? = null, // Base64 encoded credential ID
    val transports: List<String>? = null // e.g., ["internal", "hybrid"]
)

@Serializable
data class AuthenticatorSelectionCriteria(
    val authenticatorAttachment: String? = null, // e.g., "platform", "cross-platform"
    val residentKey: String? = null, // e.g., "discouraged", "preferred", "required"
    val requireResidentKey: Boolean? = null, // Note: residentKey is preferred over this now
    val userVerification: String? = null // e.g., "required", "preferred", "discouraged"
)

// --- /auth/register/verify ---

@Serializable
data class RegisterVerifyRequest(
    val credential: CreatedCredential, // The newly created credential from FIDO API
    val email: String,
    @SerialName("invite_token") val inviteToken: String? = null,
    @SerialName("device_name") val deviceName: String? = null
)

/**
 * Represents the PublicKeyCredential structure returned by the WebAuthn create() call,
 * ready to be sent to the backend for verification.
 * Field names match common JS WebAuthn libraries and expected backend formats.
 */
@Serializable
data class CreatedCredential(
    val id: String, // Base64URL encoded credential ID
    val rawId: String, // Base64URL encoded raw ID
    val type: String, // Should be "public-key"
    val authenticatorAttachment: String? = null, // Added based on example, "platform"
    val response: AuthenticatorAttestationResponse
)

@Serializable
data class AuthenticatorAttestationResponse(
    val clientDataJSON: String, // Base64URL encoded client data
    val attestationObject: String // Base64URL encoded attestation object
)

@Serializable
data class RegisterVerifyResponse(
    val success: Boolean? = null,
    val message: String? = null,
    @SerialName("session_token") val sessionToken: String? = null,
    @SerialName("person_id") val personId: String? = null // Matches example response (user_id)
) 