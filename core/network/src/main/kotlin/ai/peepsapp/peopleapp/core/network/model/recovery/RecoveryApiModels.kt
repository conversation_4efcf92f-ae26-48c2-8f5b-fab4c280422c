package ai.peepsapp.peopleapp.core.network.model.recovery

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Contextual
// Import FIDO2 types if they are directly part of these models, otherwise they are handled during conversion
// For example, if PublicKeyCredentialCreationOptions is used directly from the FIDO SDK later,
// you might not need to fully mirror its structure here for serialization if your server sends a compatible subset.

// For /auth/recovery/initiate
@Serializable
data class InitiateRecoveryRequest(val email: String)

@Serializable
data class InitiateRecoveryResponse(
    val token: String,
    @SerialName("user_id") val personId: String,
    // Optional fields from server response, can be added if needed later
    // val success: Boolean? = null,
    // val message: String? = null,
    // @SerialName("expires_at") val expiresAt: String? = null
)

// For /auth/recovery/register/challenge
@Serializable
data class RecoveryChallengeRequest(
    val token: String,
    @SerialName("is_recovery") val isRecovery: Boolean = true
)

// New structure to match server's nested challenge response
@Serializable
data class ServerChallengeContainer(
    val options: PublicKeyOptionsContainer,
    @SerialName("user_id") val userId: String // Capturing the top-level user_id from challenge response
)

@Serializable
data class PublicKeyOptionsContainer(
    @SerialName("publicKey") val publicKeyData: ServerChallengeData // Renamed from ServerChallengeResponse to avoid confusion
)

// This class now represents the actual FIDO options under "options.publicKey"
@Serializable
data class ServerChallengeData(
    val rp: RpEntity,
    val user: UserEntity,
    val challenge: String, // Base64URL encoded
    val pubKeyCredParams: List<PubKeyCredParamsEntity>,
    val timeout: Long? = null,
    val excludeCredentials: List<ExcludeCredentialEntity>? = null,
    val authenticatorSelection: AuthenticatorSelectionCriteriaEntity? = null,
    val attestation: String? = null // e.g., "direct", "indirect", "none"
    // Ensure AuthenticationExtensionsClientInputs is serializable if used
    // @SerialName("extensions") val authenticationExtensions: AuthenticationExtensionsClientInputs? = null
) {
    @Serializable
    data class RpEntity(val id: String, val name: String)

    @Serializable
    data class UserEntity(
        val id: String, // Base64URL encoded user handle
        val name: String,
        val displayName: String
    )

    @Serializable
    data class PubKeyCredParamsEntity(val type: String, val alg: Int) // e.g. type "public-key", alg -7 (ES256)

    @Serializable
    data class ExcludeCredentialEntity(
        val type: String,
        val id: String, // Base64URL encoded
        val transports: List<String>? = null
    )

    @Serializable
    data class AuthenticatorSelectionCriteriaEntity(
        val authenticatorAttachment: String? = null, // e.g., "platform", "cross-platform"
        val requireResidentKey: Boolean? = null,
        val residentKey: String? = null, // e.g., "required", "preferred", "discouraged"
        val userVerification: String // e.g., "required", "preferred", "discouraged"
    )
}

// For /auth/recovery/register/verify
// This structure should match what your server expects for the /verify endpoint.
// The `credential` field will be constructed from the Android FIDO SDK's response.
@Serializable
data class VerifyRecoveryRequest(
    val token: String,
    val credential: AttestationResponseForVerify,
    @SerialName("device_name") val deviceName: String
)

@Serializable
data class AttestationResponseForVerify(
    val id: String, // Credential ID, often base64url of rawId
    val rawId: String, // Base64URL encoded raw ID
    val response: ClientDataAndAttestationObjectForVerify,
    val type: String, // Should be "public-key"
    // Ensure AuthenticationExtensionsClientOutputs is serializable if used and sent
    // @SerialName("clientExtensionResults") val clientExtensionResults: AuthenticationExtensionsClientOutputs? = null,
    @SerialName("authenticatorAttachment") val authenticatorAttachment: String? = null // Optional, e.g. "platform"
) {
    @Serializable
    data class ClientDataAndAttestationObjectForVerify(
        val clientDataJSON: String, // Base64URL encoded
        val attestationObject: String // Base64URL encoded
    )
}

@Serializable
data class VerifyRecoveryResponse(
    @SerialName("success") val success: Boolean? = null,
    val message: String? = null,
    // Add any other fields your API returns upon successful registration
    @SerialName("session_token") val sessionToken: String? = null, // Assuming session token might be returned
    @SerialName("person_id") val personId: String? = null // This was pre-existing, matches server's optional person_id on verify
)

// Data classes for FIDO registration response from authenticator
@Serializable
data class FidoRegistrationResponseData(
    val id: String,
    val rawId: String, // This is already Base64URL from the authenticator
    val response: FidoAttestationResponsePart,
    val type: String,
    val clientExtensionResults: Map<String, @Contextual Any?>? = null, 
    val authenticatorAttachment: String? = null
)

@Serializable
data class FidoAttestationResponsePart(
    val clientDataJSON: String, // This is already Base64URL
    val attestationObject: String // This is already Base64URL
) 