package ai.peepsapp.peopleapp.core.network.model.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// --- /auth/login/challenge ---

@Serializable
data class LoginChallengeRequest(val email: String)

@Serializable
data class LoginChallengeResponse(
    val options: LoginPublicKeyCredentialRequestOptionsContainer
)

@Serializable
data class LoginPublicKeyCredentialRequestOptionsContainer(
    @SerialName("publicKey") val publicKey: LoginPublicKeyCredentialRequestOptions
)

@Serializable
data class LoginPublicKeyCredentialRequestOptions(
    val challenge: String, // Base64URL encoded
    val timeout: Long? = null,
    @SerialName("rpId") val rpId: String? = null,
    @SerialName("allowCredentials") val allowCredentials: List<LoginCredentialDescriptor>? = null,
    @SerialName("userVerification") val userVerification: String? = null // e.g., "required", "preferred", "discouraged"
)

@Serializable
data class LoginCredentialDescriptor(
    val type: String, // e.g., "public-key"
    val id: String, // Base64URL encoded
    val transports: List<String>? = null // e.g., ["internal", "hybrid"]
)

// --- /auth/login/verify ---

@Serializable
data class LoginVerifyRequest(
    val credential: AssertedCredential,
    val email: String,
    val os: String = "Android", // Defaulting as per doc example
    val browser: String = "Android App" // Defaulting as per doc example
)

/**
 * Represents the PublicKeyCredential structure returned by the WebAuthn get() call (assertion),
 * ready to be sent to the backend for verification.
 */
@Serializable
data class AssertedCredential(
    val id: String, // Base64URL encoded credential ID
    val rawId: String, // Base64URL encoded raw ID
    val type: String, // Should be "public-key"
    @SerialName("authenticatorAttachment") val authenticatorAttachment: String? = null, // Optional, e.g. "platform"
    val response: AuthenticatorAssertionResponse
)

@Serializable
data class AuthenticatorAssertionResponse(
    @SerialName("clientDataJSON") val clientDataJSON: String, // Base64URL encoded
    @SerialName("authenticatorData") val authenticatorData: String, // Base64URL encoded
    val signature: String, // Base64URL encoded
    @SerialName("userHandle") val userHandle: String? = null // Base64URL encoded, optional
)

@Serializable
data class LoginVerifyResponse(
    @SerialName("user_id") val userId: String,
    @SerialName("session_token") val sessionToken: String
)

// Data class for FIDO assertion response from authenticator (for parsing client-side JSON)
@Serializable
data class FidoAssertionResponseData(
    val id: String,
    val rawId: String,
    val type: String,
    val authenticatorAttachment: String? = null,
    val response: FidoAuthenticatorAssertionResponsePart
)

@Serializable
data class FidoAuthenticatorAssertionResponsePart(
    val clientDataJSON: String,
    val authenticatorData: String,
    val signature: String,
    val userHandle: String?
) 