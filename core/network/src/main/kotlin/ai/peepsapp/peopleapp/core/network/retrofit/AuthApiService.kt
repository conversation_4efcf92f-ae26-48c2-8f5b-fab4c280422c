package ai.peepsapp.peopleapp.core.network.retrofit

import ai.peepsapp.peopleapp.core.network.model.RegisterChallengeRequest
import ai.peepsapp.peopleapp.core.network.model.RegisterChallengeResponse
import ai.peepsapp.peopleapp.core.network.model.RegisterVerifyRequest
import ai.peepsapp.peopleapp.core.network.model.RegisterVerifyResponse
import ai.peepsapp.peopleapp.core.network.model.recovery.InitiateRecoveryRequest
import ai.peepsapp.peopleapp.core.network.model.recovery.InitiateRecoveryResponse
import ai.peepsapp.peopleapp.core.network.model.recovery.RecoveryChallengeRequest
import ai.peepsapp.peopleapp.core.network.model.recovery.ServerChallengeContainer
import ai.peepsapp.peopleapp.core.network.model.recovery.VerifyRecoveryRequest
import ai.peepsapp.peopleapp.core.network.model.recovery.VerifyRecoveryResponse
import ai.peepsapp.peopleapp.core.network.model.login.LoginChallengeRequest
import ai.peepsapp.peopleapp.core.network.model.login.LoginChallengeResponse
import ai.peepsapp.peopleapp.core.network.model.login.LoginVerifyRequest
import ai.peepsapp.peopleapp.core.network.model.login.LoginVerifyResponse
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.Response
import okhttp3.ResponseBody

/**
 * Retrofit API service interface for authentication related endpoints.
 */
interface AuthApiService {
    @POST("auth/register/challenge")
    suspend fun postRegisterChallenge(
        @Body request: RegisterChallengeRequest
    ): RegisterChallengeResponse // Assuming backend returns the response directly

    @POST("auth/register/verify")
    suspend fun postRegisterVerify(
        @Body request: RegisterVerifyRequest
    ): RegisterVerifyResponse

    // --- Passkey Recovery Endpoints ---

    /**
     * Initiates the passkey recovery process.
     * IMPORTANT: This endpoint is assumed to validate the email and return a token directly
     * without actually sending an email. The user is expected to have access to the
     * provided email account to proceed with subsequent steps using this token.
     */
    @POST("auth/recovery/initiate")
    suspend fun postInitiateRecovery(@Body request: InitiateRecoveryRequest): InitiateRecoveryResponse

    @POST("auth/recovery/register/challenge")
    suspend fun postRecoveryRegisterChallenge(@Body request: RecoveryChallengeRequest): ServerChallengeContainer

    @POST("auth/recovery/register/verify")
    suspend fun postRecoveryRegisterVerify(@Body request: VerifyRecoveryRequest): VerifyRecoveryResponse

    @POST("auth/recovery/register/verify")
    suspend fun postRecoveryRegisterVerifyRaw(@Body request: VerifyRecoveryRequest): Response<ResponseBody>

    // --- Passkey Login Endpoints ---
    @POST("auth/login/challenge")
    suspend fun postLoginChallenge(@Body request: LoginChallengeRequest): LoginChallengeResponse

    @POST("auth/login/verify")
    suspend fun postLoginVerify(@Body request: LoginVerifyRequest): LoginVerifyResponse

    // Add other endpoints like /auth/login/challenge, /auth/login/verify etc. here later
} 