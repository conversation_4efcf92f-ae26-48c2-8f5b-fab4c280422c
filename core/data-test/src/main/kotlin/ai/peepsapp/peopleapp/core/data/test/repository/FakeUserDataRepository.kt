/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ai.peepsapp.peopleapp.core.data.test.repository

import ai.peepsapp.peopleapp.core.data.repository.UserDataRepository
import ai.peepsapp.peopleapp.core.datastore.PeepsPreferencesDataSource
import ai.peepsapp.peopleapp.core.model.data.DarkThemeConfig
import ai.peepsapp.peopleapp.core.model.data.ThemeBrand
import ai.peepsapp.peopleapp.core.model.data.UserData
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Fake implementation of the [UserDataRepository] that returns hardcoded user data.
 *
 * This allows us to run the app with fake data, without needing an internet connection or working
 * backend.
 */
class FakeUserDataRepository @Inject constructor(
    private val peepsPreferencesDataSource: PeepsPreferencesDataSource,
) : UserDataRepository {

    override val userData: Flow<UserData> =
        peepsPreferencesDataSource.userData

    override suspend fun setFollowedTopicIds(followedTopicIds: Set<String>) {
        peepsPreferencesDataSource.setFollowedTopicIds(followedTopicIds)
    }

    override suspend fun setTopicIdFollowed(followedTopicId: String, followed: Boolean) {
        peepsPreferencesDataSource.setTopicIdFollowed(followedTopicId, followed)
    }

    override suspend fun toggleFollowedTopicId(followedTopicId: String, followed: Boolean) {
        peepsPreferencesDataSource.setTopicIdFollowed(followedTopicId, followed)
    }

    override suspend fun setNewsResourceBookmarked(newsResourceId: String, bookmarked: Boolean) {
        peepsPreferencesDataSource.setNewsResourceBookmarked(newsResourceId, bookmarked)
    }

    override suspend fun updateNewsResourceBookmark(newsResourceId: String, bookmarked: Boolean) {
        peepsPreferencesDataSource.setNewsResourceBookmarked(newsResourceId, bookmarked)
    }

    override suspend fun setNewsResourceViewed(newsResourceId: String, viewed: Boolean) {
        peepsPreferencesDataSource.setNewsResourceViewed(newsResourceId, viewed)
    }

    override suspend fun clearViewedNewsResources() {
        peepsPreferencesDataSource.clearViewedNewsResources()
    }

    override suspend fun setThemeBrand(themeBrand: ThemeBrand) {
        peepsPreferencesDataSource.setThemeBrand(themeBrand)
    }

    override suspend fun setDarkThemeConfig(darkThemeConfig: DarkThemeConfig) {
        peepsPreferencesDataSource.setDarkThemeConfig(darkThemeConfig)
    }

    override suspend fun setDynamicColorPreference(useDynamicColor: Boolean) {
        peepsPreferencesDataSource.setDynamicColorPreference(useDynamicColor)
    }

    override suspend fun setShouldHideOnboarding(shouldHideOnboarding: Boolean) {
        peepsPreferencesDataSource.setShouldHideOnboarding(shouldHideOnboarding)
    }

    override suspend fun updateSession(userId: String, sessionToken: String) {
        peepsPreferencesDataSource.updateSessionData(userId, sessionToken)
    }

    override suspend fun clearSession() {
        peepsPreferencesDataSource.clearSessionData()
    }
}
