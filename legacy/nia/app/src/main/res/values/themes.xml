<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Allows us to override night specific attributes in the
         values-night folder. -->
    <style name="NightAdjusted.Theme.Nia" parent="android:Theme.Material.Light.NoActionBar" />

    <!-- The final theme we use -->
    <style name="Theme.Nia" parent="NightAdjusted.Theme.Nia">
        <item name="android:forceDarkAllowed" tools:targetApi="29">false</item>
    </style>

    <style name="NightAdjusted.Theme.Splash" parent="Theme.SplashScreen">
        <item name="android:windowLightStatusBar" tools:targetApi="23">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">true</item>
    </style>

    <style name="Theme.Nia.Splash" parent="NightAdjusted.Theme.Splash">
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_splash</item>
        <item name="postSplashScreenTheme">@style/Theme.Nia</item>
    </style>

</resources>
