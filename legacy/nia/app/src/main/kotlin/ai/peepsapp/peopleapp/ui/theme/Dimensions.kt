package ai.peepsapp.peopleapp.ui.theme

import androidx.compose.ui.unit.dp

object Dimensions {
    // Spacing
    val spacing_2 = 2.dp
    val spacing_4 = 4.dp
    val spacing_8 = 8.dp
    val spacing_12 = 12.dp
    val spacing_16 = 16.dp
    val spacing_20 = 20.dp
    val spacing_24 = 24.dp
    val spacing_32 = 32.dp
    val spacing_40 = 40.dp
    val spacing_48 = 48.dp

    // Component sizes
    val icon_size_small = 16.dp
    val icon_size_medium = 24.dp
    val icon_size_large = 32.dp
    val icon_size_xlarge = 48.dp

    // Avatar sizes
    val avatar_size_small = 32.dp
    val avatar_size_medium = 40.dp
    val avatar_size_large = 56.dp
    val avatar_size_xlarge = 80.dp

    // Button heights
    val button_height_small = 32.dp
    val button_height_medium = 40.dp
    val button_height_large = 48.dp

    // Card dimensions
    val card_elevation = 4.dp
    val card_corner_radius = 12.dp
    val card_padding = 16.dp

    // Input field dimensions
    val input_field_height = 56.dp
    val input_field_corner_radius = 8.dp

    // Bottom navigation
    val bottom_nav_height = 56.dp

    // Divider
    val divider_thickness = 1.dp

    // Tooltip
    val tooltip_corner_radius = 8.dp
    val tooltip_arrow_size = 8.dp

    // Content padding
    val screen_padding_horizontal = 16.dp
    val screen_padding_vertical = 16.dp
    val list_item_padding_vertical = 12.dp
    val list_item_padding_horizontal = 16.dp

    // Image dimensions
    val post_image_height = 200.dp
    val profile_header_height = 200.dp
} 