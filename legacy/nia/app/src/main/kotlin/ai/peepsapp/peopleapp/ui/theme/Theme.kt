package ai.peepsapp.peopleapp.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

// Color definitions from iOS app
val ShamrockGreen = Color(0xFF27955A) // Primary brand color
val ShamrockGreenDark = Color(0xFF2FB76E)

val YaleBlue = Color(0xFF0D3B66) // Secondary brand color
val YaleBlueDark = Color(0xFF1555A0)

val HunyadiYellow = Color(0xFFFCBF62) // Accent color
val HunyadiYellowDark = Color(0xFFFCBF62) // Same in dark mode

val FloralWhite = Color(0xFFFFFCF2) // Background color
val FloralWhiteDark = Color(0xFFFFFCF2) // Same in dark mode

private val LightColorScheme = lightColorScheme(
    primary = ShamrockGreen,
    onPrimary = Color.White,
    secondary = YaleBlue,
    onSecondary = Color.White,
    tertiary = HunyadiYellow,
    onTertiary = Color.Black,
    background = FloralWhite,
    onBackground = Color.Black,
    surface = Color.White,
    onSurface = Color.Black,
)

private val DarkColorScheme = darkColorScheme(
    primary = ShamrockGreenDark,
    onPrimary = Color.White,
    secondary = YaleBlueDark,
    onSecondary = Color.White,
    tertiary = HunyadiYellowDark,
    onTertiary = Color.Black,
    background = Color(0xFF121212),
    onBackground = Color.White,
    surface = Color(0xFF242424),
    onSurface = Color.White,
)

@Composable
fun PeepsAppTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
} 