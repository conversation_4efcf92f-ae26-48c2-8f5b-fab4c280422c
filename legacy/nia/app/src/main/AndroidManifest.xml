<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <!--
    Firebase automatically adds these AD_ID and ADSERVICES permissions, even though we don't use them.
    If you use these permissions you must declare how you're using them to Google Play, otherwise the
    app will be rejected when publishing it. To avoid this we remove the permissions entirely.
    -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" tools:node="remove"/>
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" tools:node="remove"/>

    <application
        android:name=".NiaApplication"
        android:allowBackup="true"
        android:enableOnBackInvokedCallback="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Nia.Splash">
        <profileable android:shell="true" tools:targetApi="q" />

        <activity
            android:name=".MainActivity"
            android:configChanges="uiMode"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="www.nowinandroid.apps.samples.google.com" />
            </intent-filter>
        </activity>

        <!-- Disable Firebase analytics by default. This setting is overwritten for the `prod`
        flavor -->
        <meta-data android:name="firebase_analytics_collection_deactivated" android:value="true" />
        <!-- Disable collection of AD_ID for all build variants -->
        <meta-data android:name="google_analytics_adid_collection_enabled" android:value="false" />
        <!-- Firebase automatically adds the following property which we don't use so remove it -->
        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            tools:node="remove" />

    </application>

</manifest>
