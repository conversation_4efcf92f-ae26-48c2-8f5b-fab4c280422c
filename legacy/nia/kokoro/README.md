**Kokoro** is a continuous integration (CI) and continuous delivery (CD) system used internally at Google. It is **not a general-purpose or open-source CI tool**—it’s specific to Google’s infrastructure.

---

## **What is the `kokoro/` Folder?**

- The `kokoro/` folder contains configuration files and scripts for running builds and tests on Google’s Kokoro CI system.
- You’ll see files like:
  - `kokoro/build.sh`, `kokoro/nightly.sh` — Shell scripts for building/testing the project.
  - `kokoro/continuous.cfg`, `kokoro/nightly.cfg` — Kokoro-specific config files.

---

## **Do You Need It?**

**Short answer: No, unless you are building and testing your app inside Google’s infrastructure.**

- If you are using **GitHub Actions, GitLab CI, CircleCI, Jenkins, or any other public CI/CD system**, you do **not** need the `kokoro/` folder.
- The scripts and configs in `kokoro/` are not used by standard open-source or enterprise CI/CD systems.

---

## **What Should You Do?**

- **You can safely remove the `kokoro/` folder** from your project if you are not using Google’s internal CI.
- Instead, use `.github/workflows/` (GitHub Actions) or your preferred CI/CD system for automated builds, tests, and releases.

---

## **Summary Table**

| Folder/File      | Purpose                        | Needed for PeepsApp?         |
|------------------|-------------------------------|-----------------------------|
| `kokoro/`        | Google-internal CI/CD configs  | ❌ (unless you work at Google)|
| `.github/`       | GitHub Actions workflows       | ✅ (for most open-source/dev) |

---

**Conclusion:**  
- **Kokoro is for Google-internal CI/CD only.**
- **You do not need it** for PeepsApp unless you are building inside Google.
- Use your own CI/CD system (e.g., GitHub Actions) for builds and tests.

Would you like help setting up or customizing a modern CI workflow for your project?
