androidx.activity:activity-compose:1.9.3
androidx.activity:activity-ktx:1.9.3
androidx.activity:activity:1.9.3
androidx.annotation:annotation-experimental:1.4.1
androidx.annotation:annotation-jvm:1.9.1
androidx.annotation:annotation:1.9.1
androidx.appcompat:appcompat-resources:1.6.1
androidx.arch.core:core-common:2.2.0
androidx.arch.core:core-runtime:2.2.0
androidx.autofill:autofill:1.0.0
androidx.browser:browser:1.8.0
androidx.collection:collection-jvm:1.5.0-beta03
androidx.collection:collection-ktx:1.5.0-beta03
androidx.collection:collection:1.5.0-beta03
androidx.compose.animation:animation-android:1.8.0-beta02
androidx.compose.animation:animation-core-android:1.8.0-beta02
androidx.compose.animation:animation-core:1.8.0-beta02
androidx.compose.animation:animation:1.8.0-beta02
androidx.compose.foundation:foundation-android:1.8.0-beta02
androidx.compose.foundation:foundation-layout-android:1.8.0-beta02
androidx.compose.foundation:foundation-layout:1.8.0-beta02
androidx.compose.foundation:foundation:1.8.0-beta02
androidx.compose.material3.adaptive:adaptive-android:1.1.0-rc01
androidx.compose.material3.adaptive:adaptive:1.1.0-rc01
androidx.compose.material3:material3-adaptive-navigation-suite-android:1.4.0-alpha08
androidx.compose.material3:material3-adaptive-navigation-suite:1.4.0-alpha08
androidx.compose.material3:material3-android:1.4.0-alpha08
androidx.compose.material3:material3:1.4.0-alpha08
androidx.compose.material:material-icons-core-android:1.7.8
androidx.compose.material:material-icons-core:1.7.8
androidx.compose.material:material-icons-extended-android:1.7.8
androidx.compose.material:material-icons-extended:1.7.8
androidx.compose.material:material-ripple-android:1.8.0-beta02
androidx.compose.material:material-ripple:1.8.0-beta02
androidx.compose.runtime:runtime-android:1.8.0-beta02
androidx.compose.runtime:runtime-saveable-android:1.8.0-beta02
androidx.compose.runtime:runtime-saveable:1.8.0-beta02
androidx.compose.runtime:runtime:1.8.0-beta02
androidx.compose.ui:ui-android:1.8.0-beta02
androidx.compose.ui:ui-geometry-android:1.8.0-beta02
androidx.compose.ui:ui-geometry:1.8.0-beta02
androidx.compose.ui:ui-graphics-android:1.8.0-beta02
androidx.compose.ui:ui-graphics:1.8.0-beta02
androidx.compose.ui:ui-text-android:1.8.0-beta02
androidx.compose.ui:ui-text:1.8.0-beta02
androidx.compose.ui:ui-tooling-preview-android:1.8.0-beta02
androidx.compose.ui:ui-tooling-preview:1.8.0-beta02
androidx.compose.ui:ui-unit-android:1.8.0-beta02
androidx.compose.ui:ui-unit:1.8.0-beta02
androidx.compose.ui:ui-util-android:1.8.0-beta02
androidx.compose.ui:ui-util:1.8.0-beta02
androidx.compose.ui:ui:1.8.0-beta02
androidx.compose:compose-bom-alpha:2025.02.00
androidx.concurrent:concurrent-futures:1.1.0
androidx.core:core-ktx:1.13.1
androidx.core:core:1.13.1
androidx.customview:customview-poolingcontainer:1.0.0
androidx.customview:customview:1.0.0
androidx.emoji2:emoji2:1.4.0
androidx.exifinterface:exifinterface:1.3.7
androidx.fragment:fragment:1.5.1
androidx.graphics:graphics-path:1.0.1
androidx.graphics:graphics-shapes-android:1.0.1
androidx.graphics:graphics-shapes:1.0.1
androidx.interpolator:interpolator:1.0.0
androidx.lifecycle:lifecycle-common-java8:2.8.7
androidx.lifecycle:lifecycle-common-jvm:2.8.7
androidx.lifecycle:lifecycle-common:2.8.7
androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7
androidx.lifecycle:lifecycle-livedata-core:2.8.7
androidx.lifecycle:lifecycle-livedata:2.8.7
androidx.lifecycle:lifecycle-process:2.8.7
androidx.lifecycle:lifecycle-runtime-android:2.8.7
androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7
androidx.lifecycle:lifecycle-runtime-compose:2.8.7
androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7
androidx.lifecycle:lifecycle-runtime-ktx:2.8.7
androidx.lifecycle:lifecycle-runtime:2.8.7
androidx.lifecycle:lifecycle-viewmodel-android:2.8.7
androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7
androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7
androidx.lifecycle:lifecycle-viewmodel:2.8.7
androidx.loader:loader:1.0.0
androidx.metrics:metrics-performance:1.0.0-beta01
androidx.profileinstaller:profileinstaller:1.4.0
androidx.savedstate:savedstate-ktx:1.2.1
androidx.savedstate:savedstate:1.2.1
androidx.startup:startup-runtime:1.1.1
androidx.tracing:tracing-ktx:1.3.0-alpha02
androidx.tracing:tracing:1.3.0-alpha02
androidx.vectordrawable:vectordrawable-animated:1.1.0
androidx.vectordrawable:vectordrawable:1.1.0
androidx.versionedparcelable:versionedparcelable:1.1.1
androidx.viewpager:viewpager:1.0.0
androidx.window.extensions.core:core:1.0.0
androidx.window:window-core-android:1.3.0
androidx.window:window-core:1.3.0
androidx.window:window:1.3.0
com.google.accompanist:accompanist-drawablepainter:0.32.0
com.google.code.findbugs:jsr305:3.0.2
com.google.dagger:dagger-lint-aar:2.56
com.google.dagger:dagger:2.56
com.google.dagger:hilt-android:2.56
com.google.dagger:hilt-core:2.56
com.google.guava:listenablefuture:1.0
com.squareup.okhttp3:okhttp:4.12.0
com.squareup.okio:okio-jvm:3.9.0
com.squareup.okio:okio:3.9.0
io.coil-kt:coil-base:2.7.0
io.coil-kt:coil-compose-base:2.7.0
io.coil-kt:coil-compose:2.7.0
io.coil-kt:coil:2.7.0
jakarta.inject:jakarta.inject-api:2.0.1
javax.inject:javax.inject:1
org.jetbrains.kotlin:kotlin-stdlib-common:2.1.10
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0
org.jetbrains.kotlin:kotlin-stdlib:2.1.10
org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1
org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.8.1
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1
org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.1
org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.6.1
org.jetbrains.kotlinx:kotlinx-datetime:0.6.1
org.jetbrains:annotations:23.0.0
org.jspecify:jspecify:1.0.0
