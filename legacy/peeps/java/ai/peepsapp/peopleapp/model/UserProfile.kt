package ai.peepsapp.peopleapp.model

import android.net.Uri

/**
 * Data class representing a user profile
 */
data class UserProfile(
    val id: String = "",
    val name: String = "",
    val role: String = "",
    val company: String = "",
    val bio: String = "",
    val photoUri: Uri? = null,
    val socials: Map<SocialMediaType, String> = emptyMap()
)

/**
 * Enum representing different types of social media platforms
 */
enum class SocialMediaType {
    LINKEDIN,
    TWITTER,
    INSTAGRAM,
    GITHUB,
    WEBSITE,
    OTHER
}
