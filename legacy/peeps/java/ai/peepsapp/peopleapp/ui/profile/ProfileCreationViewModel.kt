package ai.peepsapp.peopleapp.ui.profile

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.peepsapp.peopleapp.model.SocialMediaType
import ai.peepsapp.peopleapp.model.UserProfile
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.util.UUID

class ProfileCreationViewModel : ViewModel() {
    private val _uiState = MutableStateFlow<ProfileCreationState>(ProfileCreationState.Initial)
    val uiState: StateFlow<ProfileCreationState> = _uiState

    // Temporary photo URI storage
    private var _photoUri: Uri? = null
    val photoUri: Uri? get() = _photoUri

    fun setPhotoUri(uri: Uri?) {
        _photoUri = uri
    }

    fun createProfile(
        name: String,
        role: String,
        company: String,
        bio: String,
        socials: Map<SocialMediaType, String>
    ) {
        viewModelScope.launch {
            _uiState.value = ProfileCreationState.Loading
            try {
                // Create a user profile object
                val userProfile = UserProfile(
                    id = UUID.randomUUID().toString(),
                    name = name,
                    role = role,
                    company = company,
                    bio = bio,
                    photoUri = _photoUri,
                    socials = socials
                )

                // TODO: Save the profile to a database or repository

                _uiState.value = ProfileCreationState.Success
            } catch (e: Exception) {
                _uiState.value = ProfileCreationState.Error(e.message ?: "Unknown error")
            }
        }
    }
}

sealed class ProfileCreationState {
    object Initial : ProfileCreationState()
    object Loading : ProfileCreationState()
    object Success : ProfileCreationState()
    data class Error(val message: String) : ProfileCreationState()
}