package ai.peepsapp.peopleapp.ui.profile

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import ai.peepsapp.peopleapp.R
import ai.peepsapp.peopleapp.databinding.FragmentProfileCreationBinding
import ai.peepsapp.peopleapp.model.SocialMediaType
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class ProfileCreationFragment : Fragment() {
    private var _binding: FragmentProfileCreationBinding? = null
    private val binding get() = _binding!!
    private val viewModel: ProfileCreationViewModel by viewModels()

    // Activity result launcher for photo selection
    private val photoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                viewModel.setPhotoUri(uri)
                updateProfileImage(uri)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProfileCreationBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        // Setup photo selection
        binding.buttonSelectPhoto.setOnClickListener {
            openPhotoPicker()
        }

        // Setup save button
        binding.buttonSave.setOnClickListener {
            saveProfile()
        }
    }

    private fun openPhotoPicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        photoPickerLauncher.launch(intent)
    }

    private fun updateProfileImage(uri: Uri) {
        binding.imageViewProfile.setImageURI(uri)
    }

    private fun saveProfile() {
        // Get values from form fields
        val name = binding.editTextName.text.toString().trim()
        val role = binding.editTextRole.text.toString().trim()
        val company = binding.editTextCompany.text.toString().trim()
        val bio = binding.editTextBio.text.toString().trim()

        // Validate required fields
        if (name.isEmpty()) {
            Toast.makeText(requireContext(), "Name is required", Toast.LENGTH_SHORT).show()
            return
        }

        // Collect social media links
        val socials = mutableMapOf<SocialMediaType, String>()

        binding.editTextLinkedIn.text.toString().trim().let {
            if (it.isNotEmpty()) socials[SocialMediaType.LINKEDIN] = it
        }

        binding.editTextTwitter.text.toString().trim().let {
            if (it.isNotEmpty()) socials[SocialMediaType.TWITTER] = it
        }

        binding.editTextGithub.text.toString().trim().let {
            if (it.isNotEmpty()) socials[SocialMediaType.GITHUB] = it
        }

        binding.editTextWebsite.text.toString().trim().let {
            if (it.isNotEmpty()) socials[SocialMediaType.WEBSITE] = it
        }

        // Create profile
        viewModel.createProfile(name, role, company, bio, socials)
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.uiState.collectLatest { state ->
                when (state) {
                    is ProfileCreationState.Initial -> {
                        binding.progressBar.isVisible = false
                    }
                    is ProfileCreationState.Loading -> {
                        binding.progressBar.isVisible = true
                    }
                    is ProfileCreationState.Success -> {
                        binding.progressBar.isVisible = false
                        Toast.makeText(requireContext(), "Profile created successfully", Toast.LENGTH_SHORT).show()
                        // Navigate back or to profile view
                        requireActivity().onBackPressed()
                    }
                    is ProfileCreationState.Error -> {
                        binding.progressBar.isVisible = false
                        Toast.makeText(requireContext(), "Error: ${state.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
