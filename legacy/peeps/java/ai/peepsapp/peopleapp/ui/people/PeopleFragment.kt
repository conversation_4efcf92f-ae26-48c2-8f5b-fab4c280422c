package ai.peepsapp.peopleapp.ui.people

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import ai.peepsapp.peopleapp.R
import ai.peepsapp.peopleapp.databinding.FragmentPeopleBinding

class PeopleFragment : Fragment() {

    private var _binding: FragmentPeopleBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPeopleBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupUI()
    }

    private fun setupUI() {
        // Set up create profile button
        binding.buttonCreateProfile.setOnClickListener {
            navigateToProfileCreation()
        }

        // TODO: Load and display profiles when available
    }

    private fun navigateToProfileCreation() {
        findNavController().navigate(R.id.action_navigation_people_to_profileCreationFragment)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}