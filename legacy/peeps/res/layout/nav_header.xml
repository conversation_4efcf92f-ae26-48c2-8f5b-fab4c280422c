<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="176dp"
    android:background="@color/primary_500"
    android:gravity="bottom"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <ImageView
        android:id="@+id/header_image"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:contentDescription="@string/profile_icon_description"
        android:src="@drawable/ic_account_circle_24dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:text="@string/app_name"
        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
        android:textSize="18sp" />

    <TextView
        android:id="@+id/header_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/profile_icon_description" />

</LinearLayout>
