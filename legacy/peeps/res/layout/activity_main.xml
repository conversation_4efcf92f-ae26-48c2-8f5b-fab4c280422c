<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Main content -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/toolbar"
                layout="@layout/toolbar" />

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="?attr/actionBarSize"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.google.android.material.bottomnavigation.BottomNavigationView
                android:id="@+id/nav_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:layout_marginEnd="0dp"
                android:background="?android:attr/windowBackground"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:menu="@menu/bottom_nav_menu"
                style="@style/Widget.App.BottomNavigationView"/>

            <fragment
                android:id="@+id/nav_host_fragment_activity_main"
                android:name="androidx.navigation.fragment.NavHostFragment"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:defaultNavHost="true"
                app:layout_constraintBottom_toTopOf="@id/nav_view"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:navGraph="@navigation/mobile_navigation" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/peeps_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center|bottom"
            android:contentDescription="@string/peeps_button_description"
            app:srcCompat="@drawable/peeps_button"
            app:backgroundTint="@android:color/transparent"
            app:elevation="6dp"
            app:fabSize="auto"
            app:maxImageSize="64dp"
            app:fabCustomSize="64dp"
            app:tint="@null"
            app:layout_anchor="@id/nav_view"
            app:layout_anchorGravity="top|center"
            android:translationY="-14dp"/>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_drawer"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/drawer_menu" />

</androidx.drawerlayout.widget.DrawerLayout>