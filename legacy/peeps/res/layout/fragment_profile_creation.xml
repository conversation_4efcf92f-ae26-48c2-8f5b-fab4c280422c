<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/textViewProfileTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/create_your_profile"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Profile Photo Section -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardViewPhoto"
            android:layout_width="120dp"
            android:layout_height="120dp"
            app:cardCornerRadius="60dp"
            app:cardElevation="4dp"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/textViewProfileTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/imageViewProfile"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_people_black_24dp"
                android:contentDescription="@string/profile_photo" />
        </androidx.cardview.widget.CardView>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonSelectPhoto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_photo"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/cardViewPhoto"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Basic Information Section -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/hint_name"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/buttonSelectPhoto">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPersonName" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutRole"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/hint_role"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutName">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextRole"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutCompany"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/hint_company"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutRole">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextCompany"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutBio"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="@string/hint_bio"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutCompany">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextBio"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="3" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Social Media Section -->
        <TextView
            android:id="@+id/textViewSocialTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/social_media_links"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutBio" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutLinkedIn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/hint_linkedin"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/textViewSocialTitle"
            app:startIconDrawable="@drawable/ic_people_black_24dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextLinkedIn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutTwitter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/hint_twitter"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutLinkedIn"
            app:startIconDrawable="@drawable/ic_people_black_24dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextTwitter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutGithub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/hint_github"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutTwitter"
            app:startIconDrawable="@drawable/ic_people_black_24dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextGithub"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayoutWebsite"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/hint_website"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutGithub"
            app:startIconDrawable="@drawable/ic_people_black_24dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextWebsite"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonSave"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="@string/create_profile"
            app:layout_constraintTop_toBottomOf="@id/inputLayoutWebsite" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>