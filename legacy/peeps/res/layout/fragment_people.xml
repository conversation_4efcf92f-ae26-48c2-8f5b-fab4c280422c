<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/textViewPeopleTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/title_people"
        android:textAlignment="center"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewEmptyState"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/no_profiles_message"
        android:textAlignment="center"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toBottomOf="@id/textViewPeopleTitle" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/buttonCreateProfile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/create_profile"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/textViewEmptyState"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewPeople"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/textViewPeopleTitle"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>