<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/navigation_people">

    <fragment
        android:id="@+id/navigation_people"
        android:name="ai.peepsapp.peopleapp.ui.people.PeopleFragment"
        android:label="@string/title_people"
        tools:layout="@layout/fragment_people">
        <action
            android:id="@+id/action_navigation_people_to_profileCreationFragment"
            app:destination="@id/profileCreationFragment" />
    </fragment>

    <fragment
        android:id="@+id/navigation_community"
        android:name="ai.peepsapp.peopleapp.ui.community.CommunityFragment"
        android:label="@string/title_community"
        tools:layout="@layout/fragment_community" />

    <fragment
        android:id="@+id/navigation_chat"
        android:name="ai.peepsapp.peopleapp.ui.chat.ChatFragment"
        android:label="@string/title_chat"
        tools:layout="@layout/fragment_chat" />

    <fragment
        android:id="@+id/navigation_notifications"
        android:name="ai.peepsapp.peopleapp.ui.notifications.NotificationsFragment"
        android:label="@string/title_notifications"
        tools:layout="@layout/fragment_notifications" />

    <fragment
        android:id="@+id/profileCreationFragment"
        android:name="ai.peepsapp.peopleapp.ui.profile.ProfileCreationFragment"
        android:label="Create Profile"
        tools:layout="@layout/fragment_profile_creation" />
</navigation>