<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Widget.App.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemPaddingTop">8dp</item>
        <item name="itemPaddingBottom">8dp</item>
        <item name="android:minHeight">64dp</item>
        <item name="itemIconSize">24dp</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.App.BottomNavigation</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.App.BottomNavigation</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
    </style>

    <style name="TextAppearance.App.BottomNavigation" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">12sp</item>
    </style>
</resources> 