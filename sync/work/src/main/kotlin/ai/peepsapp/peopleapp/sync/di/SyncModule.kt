package ai.peepsapp.peopleapp.sync.di

// Removed imports for WorkManagerSyncManager and SyncSubscriber/StubSyncSubscriber/FirebaseSyncSubscriber if they exist

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SyncModule {
    // Removed bindsSyncStatusMonitor

    // Removed bindsSyncSubscriber
} 