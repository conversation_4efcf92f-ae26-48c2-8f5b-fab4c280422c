@echo off
setlocal

echo Configuring Java environment for this session...

rem Set JAVA_HOME to your Android Studio JBR directory
set "JAVA_HOME=C:\Program Files\Android\Android Studio\jbr"

echo JAVA_HOME set to: %JAVA_HOME%

rem Add JAVA_HOME\bin to the beginning of the PATH for this session
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo PATH temporarily updated.

echo Running sdkmanager --list...
echo ================================================

rem Assuming sdkmanager is in the location you provided
C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin\sdkmanager.bat --list

echo ================================================
echo sdkmanager finished.

endlocal
echo Environment variables reverted to original for this session.

pause 