## Purpose

This rule provides a checklist and guidance when refactoring core data storage mechanisms (e.g., switching DataStore types, altering database schemas, changing repository interfaces). Its goal is to prevent cascading build failures and runtime errors by ensuring all affected parts of the codebase are updated.

## Core Principles & Checklist

When a core data component or its underlying storage mechanism is changed:

1.  **Identify All Implementations:**

    - Locate all concrete implementations of any repository interfaces related to the changed data store (e.g., `OfflineFirst...Repository`, `Test...Repository`, `Fake...Repository`).
    - **Action:** Ensure all these implementations are updated to reflect new method signatures, data types, or dependencies. Verify all interface methods are implemented.

2.  **Update Data Models & Instantiations:**

    - If data models (e.g., Kotlin data classes like `UserData`) are modified (fields added/removed, nullability changed), find all places where these models are instantiated.
    - **Action:** Update all instantiations, especially in test code (e.g., `emptyUserData` initializers, test data builders), to match the new constructor.

3.  **Synchronize Test Infrastructure:**

    - **Hilt Test Modules (`@TestInstallIn`):** Scrutinize Hilt modules in test source sets (e.g., `core/data-test`, `core/datastore-test`) that replace production modules.
      - **Action:** Ensure they provide the correct test doubles (fakes, mocks) compatible with the _new_ data system. Update their provision methods and injected dependencies.
    - **Fake/Test Repositories:** Ensure these test doubles correctly implement the (potentially changed) repository interfaces and interact with the new data system or its faked equivalent.

4.  **Verify Build Dependencies (Multi-Module & KSP):**

    - If test modules or any module using KSP (e.g., for Hilt) directly reference types from a new library (e.g., `androidx.datastore.preferences.Preferences`, specific Proto-generated classes), they often need a direct `implementation` or `api` dependency in their `build.gradle.kts`.
    - **Action:** Add missing dependencies to `build.gradle.kts` files of affected test/KSP-using modules. KSP needs these for type resolution during code generation.

5.  **Thorough Cleanup of Old System:**
    - If replacing a system (e.g., Proto DataStore with Preferences DataStore for a specific domain), remove all obsolete files.
    - **Action:** Delete old data classes, serializers, migration files, and outdated Hilt module provisions related to the _old_ system to prevent conflicts and confusion.

## Common Pitfalls to Avoid

- **Forgetting Test Code:** Test implementations and Hilt modules are a frequent source of errors after core refactoring.
- **Transitive Dependency Illusion:** Assuming a module will get a necessary dependency transitively for KSP or direct type usage in tests. It often needs to be explicit.
- **Incomplete Interface Implementation:** Leaving methods unimplemented in concrete classes after an interface changes.
- **Outdated Data Instantiations:** Test data or default objects not matching updated data class constructors.

## Example Scenario

Migrating a specific data domain from a Proto DataStore (`DataStore<OldProtoUserPreferences>`) to Preferences DataStore (`DataStore<Preferences>`). This would involve:

- Replacing `OldProtoUserPreferencesSerializer` and `.proto` definitions with `Preferences.Key` definitions.
- Updating repository implementations to use the new Preferences-based data source.
- Ensuring `TestUserDataRepository` (e.g., in `:core:testing`), `FakeUserDataRepository` (e.g., in `:core:data-test`), and their Hilt modules are updated to work with `DataStore<Preferences>`.
- Adding the `androidx.datastore:datastore-preferences` dependency to modules like `:core:datastore-test` and `:core:data-test` if their Hilt modules directly provide or use `DataStore<Preferences>`.
- Deleting all files and configurations solely related to `OldProtoUserPreferences`.

## Summary

Refactoring core data systems requires a systematic approach. Proactively check all interface implementations, test modules, data instantiations, and build dependencies to ensure a smooth transition and avoid a cascade of build errors.
