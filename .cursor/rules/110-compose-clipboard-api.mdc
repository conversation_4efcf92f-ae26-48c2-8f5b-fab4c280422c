---
description: Compose clipboard Api Usage
globs: *.kt
alwaysApply: false
---
## Purpose

To guide the AI assistant in using the correct and current Jetpack Compose `Clipboard` API.

## Core Principle

The `androidx.compose.ui.platform.ClipboardManager` and `LocalClipboardManager` are deprecated. The modern approach is to use `androidx.compose.ui.platform.Clipboard` (obtained via `LocalClipboard.current`). Setting clipboard content involves creating an `android.content.ClipData` object, wrapping it in an `androidx.compose.ui.platform.ClipEntry`, and then using the `setClipEntry()` method on the `Clipboard` instance. This is often done within a coroutine scope.

Refer to the official documentation: [https://developer.android.com/reference/kotlin/androidx/compose/ui/platform/Clipboard](mdc:https:/developer.android.com/reference/kotlin/androidx/compose/ui/platform/Clipboard)

## Detection of Deprecated API

When reviewing or generating Composable code that interacts with the clipboard:

1.  **Identify Deprecated Usage:**
    *   Look for `LocalClipboardManager.current`.
    *   Look for direct calls to `clipboardManager.setText(AnnotatedString(...))` if `clipboardManager` is an old `ClipboardManager` type.

    <example type="invalid">
    ```kotlin
    // In Composable - OLD DEPRECATED WAY
    val clipboardManager: androidx.compose.ui.platform.ClipboardManager = LocalClipboardManager.current // Deprecated
    Button(onClick = { clipboardManager.setText(AnnotatedString("Copied text")) }) { /* ... */ }
    ```
    </example>

## Guidance for Modern API

1.  **Use `LocalClipboard.current`:**
    *   Obtain the `Clipboard` instance using `val clipboard: Clipboard = LocalClipboard.current`.

2.  **Use `setClipEntry(ClipEntry(ClipData))`:**
    *   To set text content, create an `android.content.ClipData` object, typically using `ClipData.newPlainText(label, textToCopy)`.
    *   Wrap this `ClipData` in an `androidx.compose.ui.platform.ClipEntry`.
    *   Call `clipboard.setClipEntry(ClipEntry(yourClipData))` (this is a suspend function).
    *   It's standard practice to launch this in a coroutine scope using `rememberCoroutineScope()`.

    <example>
    ```kotlin
    // In Composable - NEW CORRECT WAY (for compose.ui version 1.8.0+)
    import android.content.ClipData // Required
    import androidx.compose.ui.platform.ClipEntry // Required
    import androidx.compose.ui.platform.Clipboard
    import androidx.compose.ui.platform.LocalClipboard
    import androidx.compose.runtime.rememberCoroutineScope
    import kotlinx.coroutines.launch

    // ...

    val clipboard: Clipboard = LocalClipboard.current
    val coroutineScope = rememberCoroutineScope()
    val textToCopy = "This is my text"

    Button(onClick = {
        coroutineScope.launch {
            val clipData = ClipData.newPlainText("label", textToCopy) // "label" is optional
            clipboard.setClipEntry(ClipEntry(clipData))
            // Optionally show a Snackbar/Toast here
        }
    }) {
        Text("Copy Text")
    }
    ```
    </example>

3.  **Getting Text (if needed):**
    *   The `Clipboard` interface also has a `suspend fun getClipEntry(): ClipEntry?` method. You can then access `clipEntry.clipData.getItemAt(0).text`.

## Summary

- **Avoid:** `LocalClipboardManager`.
- **Prefer:** `LocalClipboard.current` to get a `Clipboard` instance.
- **To set text/content:** Use `ClipData.newPlainText(...)` to create `ClipData`, wrap it in `ClipEntry(...)`, and then call `clipboard.setClipEntry(yourClipEntry)`, typically within a `rememberCoroutineScope().launch { ... }`.
- Ensure `import android.content.ClipData` and `import androidx.compose.ui.platform.ClipEntry` are present.
