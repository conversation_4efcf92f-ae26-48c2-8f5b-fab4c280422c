---
description: Jetpack compose state management
globs: *.kt
alwaysApply: false
---
## Purpose

This rule guides the AI assistant in correctly handling and advising on Jetpack Compose state management patterns, specifically when state is exposed from a ViewModel. The goal is to prevent common errors related to state collection and ensure clear, efficient state observation in Composables.

## Core Principle

When a ViewModel exposes a `State` object directly (e.g., using `var propertyName by mutableStateOf(...)`), Composables should access this state property *directly*. Using `.collectAsState()` or `.collectAsStateWithLifecycle()` on such a property is incorrect and unnecessary, as the property itself is already a `State` object that Compose automatically tracks.

## Detection and Advice

When reviewing or generating Composable code that interacts with a ViewModel:

1.  **Identify ViewModel State Declaration:**
    *   Check if the ViewModel declares state properties like:
        ```kotlin
        // In ViewModel
        var myStateProperty by mutableStateOf("initialValue")
        var anotherState by mutableStateOf(0)
        ```

2.  **Check Composable Usage:**
    *   If a Composable attempts to collect such a property:
        <example type="invalid">
        ```kotlin
        // In Composable - INCORRECT
        @Composable
        fun MyScreen(viewModel: MyViewModel = hiltViewModel()) {
            val stateValue by viewModel.myStateProperty.collectAsStateWithLifecycle() // Incorrect
            val anotherValue by viewModel.anotherState.collectAsState()             // Incorrect
            Text("State: $stateValue, Another: $anotherValue")
        }
        ```
        </example>

3.  **Provide Correction/Guidance:**
    *   **Advise direct access.** The correct way is to read the ViewModel property directly:
        <example>
        ```kotlin
        // In Composable - CORRECT
        @Composable
        fun MyScreen(viewModel: MyViewModel = hiltViewModel()) {
            val stateValue = viewModel.myStateProperty // Correct direct access
            val anotherValue = viewModel.anotherState   // Correct direct access
            Text("State: $stateValue, Another: $anotherValue")
        }
        ```
        </example>
    *   **Explanation:** Remind the user that `by mutableStateOf` in the ViewModel already makes the property a `State` object that Compose can observe. `collectAsState...()` is intended for `Flow` types (like `StateFlow`).

## When to Use `collectAsStateWithLifecycle()`

This method (or `collectAsState()`) IS appropriate when the ViewModel exposes a `Flow` (typically a `StateFlow`):

<example>
```kotlin
// In ViewModel
class MyViewModel : ViewModel() {
    private val _dataFlow = MutableStateFlow("initial data")
    val dataFlow: StateFlow<String> = _dataFlow.asStateFlow()
}

// In Composable
@Composable
fun MyOtherScreen(viewModel: MyViewModel = hiltViewModel()) {
    val dataValue by viewModel.dataFlow.collectAsStateWithLifecycle() // Correct for StateFlow
    Text("Data from Flow: $dataValue")
}
```
</example>

## Summary

- **ViewModel `var prop by mutableStateOf(...)`**: Access directly in Composable (`viewModel.prop`).
- **ViewModel `val flowProp: StateFlow<...>`**: Collect in Composable (`viewModel.flowProp.collectAsStateWithLifecycle()`).
- Refer to `docs/guides/compose_state_management_practices.md` for more comprehensive guidelines on state management in this project.
