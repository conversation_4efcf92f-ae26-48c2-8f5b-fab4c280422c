﻿# Copyright (c) 1993-2009 Microsoft Corp.
#
# This is a sample HOSTS file used by Microsoft TCP/IP for Windows.
#
# This file contains the mappings of IP addresses to host names. Each
# entry should be kept on an individual line. The IP address should
# be placed in the first column followed by the corresponding host name.
# The IP address and the host name should be separated by at least one
# space.
#
# Additionally, comments (such as these) may be inserted on individual
# lines or following the machine name denoted by a '#' symbol.
#
# For example:
#
#      ************     rhino.acme.com          # source server
#       ***********     x.acme.com              # x client host

# localhost name resolution is handled within DNS itself.
#	127.0.0.1       localhost
#	::1             localhost


*********** www.msftconnecttest.com
*********** msftconnecttest.com
# PhoenixNAP Servers
*************	alpha
**************	beta
*************	charlie
**************	delta
#*************** epsilon
************** epsilon
#*************** gamma
************* gamma
#************* gamma
************** kappa
*************** lambda
************** mu
************* orion

# Hetzner
# ************ delta
************** iota

# Added by Dock<PERSON> Desktop
************ host.docker.internal
************ gateway.docker.internal
# To allow the same kube context to work on the host and the container:
127.0.0.1 kubernetes.docker.internal

************** travelbox.oauth.com

127.0.0.1 local.peepsapp.ai

# End of section



